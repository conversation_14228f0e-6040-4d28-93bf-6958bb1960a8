{"cmake": {"generator": {"multiConfig": false, "name": "Ninja"}, "paths": {"cmake": "/Users/<USER>/Library/Android/sdk/cmake/3.18.1/bin/cmake", "cpack": "/Users/<USER>/Library/Android/sdk/cmake/3.18.1/bin/cpack", "ctest": "/Users/<USER>/Library/Android/sdk/cmake/3.18.1/bin/ctest", "root": "/Users/<USER>/Library/Android/sdk/cmake/3.18.1/share/cmake-3.18"}, "version": {"isDirty": false, "major": 3, "minor": 18, "patch": 1, "string": "3.18.1-g262b901", "suffix": "g262b901"}}, "objects": [{"jsonFile": "codemodel-v2-2280d30c51711689e32f.json", "kind": "codemodel", "version": {"major": 2, "minor": 1}}, {"jsonFile": "cache-v2-0cb2320e3fc79d0063d1.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, {"jsonFile": "cmakeFiles-v1-36998b893dc2d192035b.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}], "reply": {"client-agp": {"cache-v2": {"jsonFile": "cache-v2-0cb2320e3fc79d0063d1.json", "kind": "cache", "version": {"major": 2, "minor": 0}}, "cmakeFiles-v1": {"jsonFile": "cmakeFiles-v1-36998b893dc2d192035b.json", "kind": "cmakeFiles", "version": {"major": 1, "minor": 0}}, "codemodel-v2": {"jsonFile": "codemodel-v2-2280d30c51711689e32f.json", "kind": "codemodel", "version": {"major": 2, "minor": 1}}}}}