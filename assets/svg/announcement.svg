<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" version="1.1" width="256" height="256" viewBox="0 0 256 256" xml:space="preserve">
<g style="stroke: none; stroke-width: 0; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: none; fill-rule: nonzero; opacity: 1;" transform="translate(1.4065934065934016 1.4065934065934016) scale(2.81 2.81)">
	<path d="M 33.29 9.989 c -2.787 15.961 -8.325 29.168 -16.617 39.624 l -7.431 7.431 c -5.387 5.387 -5.387 14.121 0 19.508 l 0 0 c 5.387 5.387 14.121 5.387 19.508 0 l 7.431 -7.431 l 0.004 0.005 c 10.454 -8.292 23.663 -13.831 39.623 -16.617 c 2.016 -0.352 2.808 -2.821 1.36 -4.269 L 57.364 28.436 L 37.56 8.631 C 36.112 7.181 33.642 7.972 33.29 9.989 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(233,233,244); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 50.891 21.963 l 6.473 6.473 l 6.278 6.278 c 3.102 -3.203 2.812 -8.578 -0.681 -12.07 C 59.469 19.151 54.094 18.861 50.891 21.963 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(233,233,244); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 37.56 8.631 c -1.448 -1.45 -3.917 -0.659 -4.27 1.357 c -0.833 4.773 -1.918 9.295 -3.244 13.575 l 32.188 32.188 c 4.28 -1.325 8.801 -2.41 13.574 -3.243 c 2.016 -0.352 2.808 -2.821 1.361 -4.269 L 57.364 28.436 L 37.56 8.631 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(255,150,92); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 40.812 65.766 l -20.78 -20.78 c -1.066 1.595 -2.182 3.142 -3.359 4.626 l -7.431 7.431 c -5.387 5.387 -5.387 14.121 0 19.508 c 5.387 5.387 14.121 5.387 19.508 0 l 7.431 -7.431 l 0.004 0.005 C 37.67 67.948 39.217 66.833 40.812 65.766 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(255,150,92); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 36.186 69.126 l -0.004 -0.005 l -6.322 6.322 L 42.086 87.67 c 1.773 1.773 4.674 1.773 6.447 0 c 1.773 -1.773 1.773 -4.674 0 -6.447 L 36.328 69.018 C 36.282 69.055 36.232 69.089 36.186 69.126 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(233,233,244); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 18.996 81.585 c -3.789 0 -7.577 -1.441 -10.461 -4.326 c -2.794 -2.794 -4.333 -6.509 -4.333 -10.461 s 1.539 -7.667 4.333 -10.461 l 7.39 -7.391 c 8.128 -10.267 13.64 -23.432 16.38 -39.13 v 0 c 0.228 -1.306 1.154 -2.364 2.415 -2.762 c 1.256 -0.396 2.616 -0.062 3.547 0.871 l 39.608 39.608 c 0.933 0.933 1.265 2.291 0.867 3.547 c -0.398 1.262 -1.458 2.187 -2.763 2.414 C 60.262 56.238 47.083 61.76 36.81 69.907 l -7.352 7.352 C 26.574 80.142 22.784 81.585 18.996 81.585 z M 34.275 10.16 c -2.802 16.053 -8.461 29.536 -16.818 40.073 l -0.077 0.086 l -7.431 7.432 c -2.416 2.416 -3.748 5.629 -3.748 9.047 c 0 3.418 1.331 6.631 3.748 9.047 c 4.99 4.989 13.106 4.988 18.094 0 l 8.221 -8.221 l 0.084 0.105 c 10.42 -8.022 23.632 -13.473 39.288 -16.206 c 0.576 -0.101 1.024 -0.491 1.199 -1.046 c 0.174 -0.55 0.034 -1.122 -0.374 -1.53 L 36.853 9.339 c -0.409 -0.41 -0.98 -0.55 -1.531 -0.377 C 34.767 9.137 34.376 9.585 34.275 10.16 L 34.275 10.16 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 68.622 18.176 c -0.256 0 -0.512 -0.098 -0.707 -0.293 c -0.391 -0.391 -0.391 -1.023 0 -1.414 l 8.695 -8.695 c 0.391 -0.391 1.023 -0.391 1.414 0 c 0.391 0.391 0.391 1.023 0 1.414 l -8.695 8.695 C 69.134 18.079 68.878 18.176 68.622 18.176 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 58.897 12.94 c -0.059 0 -0.118 -0.005 -0.178 -0.016 c -0.543 -0.098 -0.905 -0.617 -0.808 -1.161 l 1.963 -10.94 c 0.098 -0.544 0.617 -0.907 1.161 -0.808 c 0.543 0.098 0.905 0.617 0.808 1.161 l -1.963 10.94 C 59.794 12.601 59.373 12.94 58.897 12.94 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 73.857 27.901 c -0.476 0 -0.896 -0.339 -0.983 -0.823 c -0.098 -0.544 0.265 -1.063 0.808 -1.161 l 10.939 -1.963 c 0.545 -0.094 1.063 0.264 1.161 0.808 c 0.098 0.544 -0.265 1.063 -0.808 1.161 l -10.939 1.963 C 73.975 27.897 73.916 27.901 73.857 27.901 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 63.654 36.14 L 49.466 21.952 l 0.73 -0.707 c 3.579 -3.467 9.622 -3.158 13.473 0.692 c 3.849 3.85 4.159 9.893 0.692 13.474 L 63.654 36.14 z M 52.375 22.032 l 11.198 11.199 c 1.988 -2.846 1.49 -7.071 -1.318 -9.88 C 59.444 20.54 55.219 20.044 52.375 22.032 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 61.954 56.886 L 28.913 23.844 l 0.179 -0.576 c 1.316 -4.249 2.397 -8.775 3.214 -13.452 c 0.228 -1.306 1.154 -2.364 2.415 -2.763 c 1.256 -0.396 2.616 -0.062 3.547 0.871 l 39.608 39.608 c 0.933 0.932 1.265 2.291 0.867 3.547 c -0.398 1.262 -1.458 2.187 -2.763 2.414 c -4.672 0.815 -9.197 1.896 -13.45 3.213 L 61.954 56.886 z M 31.179 23.282 L 62.516 54.62 c 4.164 -1.263 8.575 -2.303 13.12 -3.097 c 0.576 -0.101 1.024 -0.491 1.199 -1.046 c 0.174 -0.55 0.034 -1.122 -0.374 -1.53 L 36.853 9.338 c -0.408 -0.409 -0.979 -0.549 -1.53 -0.377 c -0.555 0.175 -0.946 0.624 -1.047 1.2 C 33.481 14.71 32.44 19.121 31.179 23.282 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 18.996 81.585 c -3.789 0 -7.577 -1.441 -10.461 -4.326 c -2.794 -2.794 -4.333 -6.509 -4.333 -10.461 s 1.539 -7.667 4.333 -10.462 l 7.39 -7.39 c 1.101 -1.391 2.203 -2.91 3.276 -4.516 l 0.677 -1.013 l 22.503 22.503 l -1.013 0.677 c -1.627 1.088 -3.161 2.201 -4.559 3.31 l -7.352 7.352 C 26.574 80.142 22.784 81.585 18.996 81.585 z M 20.18 46.548 c -0.897 1.299 -1.81 2.534 -2.723 3.686 l -0.077 0.086 L 9.949 57.75 c -2.416 2.417 -3.748 5.63 -3.748 9.048 c 0 3.417 1.331 6.631 3.748 9.047 c 4.99 4.989 13.106 4.988 18.094 0 l 8.221 -8.221 l 0.084 0.105 c 0.921 -0.707 1.892 -1.413 2.901 -2.11 L 20.18 46.548 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 45.31 90 c -1.488 0 -2.884 -0.576 -3.93 -1.623 L 29.152 76.15 c -0.187 -0.188 -0.293 -0.441 -0.293 -0.707 c 0 -0.266 0.105 -0.52 0.293 -0.707 l 6.322 -6.322 c 0.06 -0.06 0.126 -0.111 0.198 -0.154 c 0.398 -0.284 1.015 -0.296 1.362 0.051 L 49.24 80.515 c 1.047 1.047 1.623 2.442 1.623 3.931 c 0 1.489 -0.576 2.885 -1.623 3.931 C 48.193 89.423 46.798 90 45.31 90 z M 31.274 75.443 l 11.52 11.519 c 1.338 1.338 3.696 1.338 5.033 0 c 0.669 -0.669 1.037 -1.562 1.037 -2.517 s -0.368 -1.848 -1.037 -2.517 L 36.307 70.41 L 31.274 75.443 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
	<path d="M 17.519 69.087 c -0.256 0 -0.512 -0.098 -0.707 -0.293 c -0.391 -0.391 -0.391 -1.023 0 -1.414 l 8.22 -8.221 c 0.391 -0.391 1.023 -0.391 1.414 0 c 0.391 0.391 0.391 1.023 0 1.414 l -8.22 8.221 C 18.031 68.989 17.775 69.087 17.519 69.087 z" style="stroke: none; stroke-width: 1; stroke-dasharray: none; stroke-linecap: butt; stroke-linejoin: miter; stroke-miterlimit: 10; fill: rgb(0,0,0); fill-rule: nonzero; opacity: 1;" transform=" matrix(1 0 0 1 0 0) " stroke-linecap="round"/>
</g>
</svg>