import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

extension Riverpod<T> on AsyncValue<T> {
  //? Override .when() method to add a new case
  R get<R>({
    bool skipLoadingOnReload = false,
    bool skipLoadingOnRefresh = true,
    bool skipError = false,
    required R Function(T data) data,
    R Function(Object error, StackTrace stackTrace)? error,
    Function()? loading,
  }) {
    return when(
      skipError: skipError,
      skipLoadingOnRefresh: skipLoadingOnRefresh,
      skipLoadingOnReload: skipLoadingOnReload,
      data: data,
      error: (err, stack) {
        if (error != null) {
          return error(err, stack);
        } else {
          return const SizedBox() as R;
        }
      },
      loading: () {
        return loading != null ? loading() : const LoadingWidget();
      },
    );
  }
}

extension PaginateListener<T> on WidgetRef {
  void listenPagination<T>({
    required ProviderListenable<AsyncValue<List<T>>> provider,
    required ValueNotifier<List<T>> dataNotifier,
    required ValueNotifier<bool> isLoadingNotifier,
    ValueNotifier<bool>? isInitialLoadCompleteNotifier,
  }) {
    listen<AsyncValue<List<T>>>(provider, (previous, next) {
      next.when(
        data: (data) {
          // Append new data to the existing list
          dataNotifier.value = [...dataNotifier.value, ...data];

          // Update loading state
          isLoadingNotifier.value = false;

          // Mark initial load as complete if applicable
          if (isInitialLoadCompleteNotifier != null &&
              !isInitialLoadCompleteNotifier.value) {
            isInitialLoadCompleteNotifier.value = true;
          }
        },
        error: (error, stackTrace) {
          // Handle error state
          isLoadingNotifier.value = false;
        },
        loading: () {
          // Update loading state
          isLoadingNotifier.value = true;
        },
      );
    });
  }
}
