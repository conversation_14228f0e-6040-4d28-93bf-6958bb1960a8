import 'package:flutter_riverpod/flutter_riverpod.dart';

// * Bottom Nav Bar Controller ========================================
final stepperController = Provider<StepperController>(
  (ref) {
    return StepperController();
  },
);

// * Bottom Nav Bar State Notifier ========================================
final stepperControllerProvider = StateNotifierProvider<StepperController, int>(
  (ref) => ref.watch(stepperController),
);

class StepperController extends StateNotifier<int> {
  StepperController() : super(0);

  void changeIndex(int index) {
    state = index;
  }
}
