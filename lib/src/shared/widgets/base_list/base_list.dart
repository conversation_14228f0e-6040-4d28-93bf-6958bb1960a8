import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class BaseList<T> extends HookConsumerWidget {
  final List<T> data;
  final Widget Function(T data, int index) itemBuilder;
  final Widget separatorGap;
  final bool isHorizontal;
  final double? height;
  final EdgeInsetsGeometry? padding;
  final bool showEmptyWidget;
  final Alignment? alignment;
  final ScrollPhysics? physics;
  final Widget? emptyWidget;
  final bool isLoading;
  final Widget? loadingWidget;

  // * For Grid View
  final bool isGrid;
  final int crossAxisCount;
  final double crossAxisSpacing;
  final double mainAxisSpacing;

  // * For Pagination
  final ValueNotifier<bool>? isLoadingMore;
  final ValueNotifier<int>? page;

  const BaseList({
    super.key,
    required this.data,
    required this.itemBuilder,
    this.separatorGap = const SizedBox(
      height: AppSpaces.smallPadding,
    ),
    this.isHorizontal = false,
    this.showEmptyWidget = true,
    this.isGrid = false,
    this.height,
    this.emptyWidget,
    this.padding,
    this.physics,
    this.alignment,
    this.loadingWidget,
    this.isLoading = false,
    this.crossAxisCount = 2,
    this.crossAxisSpacing = 0,
    this.mainAxisSpacing = 0,
    this.isLoadingMore,
    this.page,
  });

  const BaseList.horizontal({
    super.key,
    required this.data,
    required this.itemBuilder,
    this.separatorGap = const SizedBox(
      width: AppSpaces.smallPadding,
    ),
    this.isHorizontal = true,
    this.physics,
    this.padding,
    this.emptyWidget,
    this.showEmptyWidget = true,
    this.isLoading = false,
    this.isGrid = false,
    required this.height,
    this.alignment,
    this.loadingWidget,
    this.crossAxisCount = 2,
    this.crossAxisSpacing = 0,
    this.mainAxisSpacing = 0,
    this.isLoadingMore,
    this.page,
  });

  const BaseList.grid({
    super.key,
    required this.data,
    required this.itemBuilder,
    this.separatorGap = const SizedBox(
      height: AppSpaces.mediumPadding,
    ),
    this.isHorizontal = false,
    this.showEmptyWidget = true,
    this.height,
    this.emptyWidget,
    this.padding,
    this.physics,
    this.alignment,
    this.loadingWidget,
    this.isLoading = false,
    this.isGrid = true,
    this.crossAxisCount = 2,
    this.crossAxisSpacing = AppSpaces.mediumPadding,
    this.mainAxisSpacing = AppSpaces.smallPadding,
    this.isLoadingMore,
    this.page,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (isLoading && data.isEmpty) {
      return loadingWidget ?? const LoadingWidget();
    }

    if (data.isEmpty && showEmptyWidget) {
      return Center(child: emptyWidget ?? Text(context.tr.noData));
    }

    if (data.isEmpty) return const SizedBox.shrink();

    Widget baseList({
      bool isHorizontal = false,
    }) {
      return BasePaginationWidget(
        isLoading: isLoading,
        onScrollEnd: () {
          if (isLoadingMore != null && page != null) {
            isLoadingMore?.value = true;
            page?.value += 1;
          }
        },
        child: ListView.separated(
          padding: padding,
          shrinkWrap: true,
          physics: physics,
          itemCount: data.length,
          scrollDirection: isHorizontal ? Axis.horizontal : Axis.vertical,
          itemBuilder: (context, index) {
            return itemBuilder(data[index], index);
          },
          separatorBuilder: (context, index) => separatorGap,
        ),
      );
    }

    if (isHorizontal) {
      return baseList(isHorizontal: true).decorated(
        height: height,
        alignment: alignment ??
            (context.isEng ? Alignment.centerLeft : Alignment.centerRight),
      );
    }

    return Stack(
      children: [
        baseList(),
        if (isLoadingMore?.value == true)
          const Positioned(
            bottom: 0,
            right: 0,
            left: 0,
            child: LoadingWidget(
              loadingType: LoadingType.linear,
            ),
          )
      ],
    );
  }
}

double _previousScrollPosition = 0;

class BasePaginationWidget extends StatelessWidget {
  final Widget child;
  final bool isLoading;
  final Function() onScrollEnd;

  const BasePaginationWidget({
    super.key,
    required this.child,
    required this.onScrollEnd,
    required this.isLoading,
  });

  @override
  Widget build(BuildContext context) {
    return NotificationListener(
      onNotification: (ScrollNotification scrollInfo) {
        if (scrollInfo.metrics.axis == Axis.horizontal) {
          return true;
        }

        if (!isLoading &&
            scrollInfo.metrics.pixels == scrollInfo.metrics.maxScrollExtent &&
            scrollInfo.metrics.pixels > _previousScrollPosition) {
          onScrollEnd();
        }

        _previousScrollPosition = scrollInfo.metrics.pixels;

        return true;
      },
      child: child,
    );
  }
}
