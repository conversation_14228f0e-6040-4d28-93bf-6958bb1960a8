part of shared_widgets;

class DetailsTopSectionWidget extends StatelessWidget {
  final String imagePath;
  final String name;
  final String description;
  final Widget? tabBarWidget;

  const DetailsTopSectionWidget(
      {super.key,
      required this.imagePath,
      required this.name,
      required this.description,
      this.tabBarWidget});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          children: [
            SizedBox(
              height: 50.h,
              width: 55.w,
              child: ClipRRect(
                borderRadius:
                    BorderRadius.circular(AppRadius.baseContainerRadius),
                child: BaseCachedImage(imagePath),
              ),
            ),
            context.smallGap,
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  name,
                  style: context.blueHint,
                ),
                context.xSmallGap,
                Text(
                  description,
                  style: context.smallHint.copyWith(fontSize: 12),
                ),
              ],
            ),
          ],
        ).paddingOnly(
          right: AppSpaces.mediumPadding,
          left: AppSpaces.mediumPadding,
          top: AppSpaces.mediumPadding,
        ),
        context.mediumGap,
        const MyDivider(),
        context.smallGap,
        tabBarWidget ?? const SizedBox(),
        context.smallGap,
        if (tabBarWidget != null) ...[
          const MyDivider(),
          context.mediumGap,
        ]
      ],
    );
  }
}
