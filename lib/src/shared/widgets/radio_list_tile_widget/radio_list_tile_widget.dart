import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';


class RadioListTileWidget extends StatelessWidget {
  final String title;
  final ValueNotifier<int> radioValue;
  final Function(dynamic)? onChanged;
  final int value;
  const RadioListTileWidget({
    super.key,
    required this.title,
    required this.radioValue,
    this.onChanged,
    required this.value,
  });

  @override
  Widget build(BuildContext context) {
    return RadioListTile<int>.adaptive(
      activeColor: ColorManager.primaryColor,
      contentPadding: const EdgeInsets.only(left: 20, right: 12),
      groupValue: radioValue.value,
      controlAffinity: ListTileControlAffinity.trailing,
      value: value,
      onChanged: onChanged,
      title: Text(
        title,
        style: context.hint,
        overflow: TextOverflow.fade,
        softWrap: false,
        maxLines: 1,
      ),
    );
  }
}
