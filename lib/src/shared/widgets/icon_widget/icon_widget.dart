import 'package:flutter/cupertino.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:xr_helper/xr_helper.dart';

class IconWidget extends StatelessWidget {
  final String icon;
  final Color? color;
  const IconWidget({
    super.key,
    required this.icon,
    this.color = ColorManager.white,
  });

  @override
  Widget build(BuildContext context) {
    return SvgPicture.asset(
      color: color,
      icon,
      height: 20,
      width: 20,

    );
  }
}
