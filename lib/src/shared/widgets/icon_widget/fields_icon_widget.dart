import 'package:flutter/cupertino.dart';
import 'package:xr_helper/xr_helper.dart';

class FieldsIconWidget extends StatelessWidget {
  final String icon;
  final Color? color;
  final double? height;
  final double? width;
  const FieldsIconWidget({
    super.key,
    required this.icon,
    this.color = ColorManager.iconColor,
    this.height = 15,
    this.width = 15,
  });

  @override
  Widget build(BuildContext context) {
    return Image.asset(
      icon,
      height: height,
      width: width,
      color: color,
    ).paddingAll(15);
  }
}
