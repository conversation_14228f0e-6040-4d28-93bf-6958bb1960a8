part of shared_widgets;

Future<void> showAlertDialog(BuildContext context,
    {required Widget child,
    String? header,
    bool isWarningMessage = true,
    required Function() onConfirm,
    bool isLoading = false}) async {
  return await showAdaptiveDialog(

      context: context,
      builder: (context) {
        return AlertDialogWidget(
          header: header,
          onConfirm: onConfirm,
          isLoading: isLoading,
          child: child,
        );
      });
}
