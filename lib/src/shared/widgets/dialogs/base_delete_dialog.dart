import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class BaseDeleteDialog extends StatelessWidget {
  final String description;
  final Function() onConfirm;
  final bool isLoading;

  const BaseDeleteDialog({
    super.key,
    required this.description,
    required this.onConfirm,
    this.isLoading = false,
  });

  @override
  Widget build(BuildContext context) {
    return Dialog(
        elevation: 0,
        surfaceTintColor: Colors.transparent,
        backgroundColor: Colors.transparent,
        insetPadding: EdgeInsets.zero,
        shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius)),
        child: BaseMainDialog(
          radius: AppRadius.baseContainerRadius,
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(
                vertical: AppSpaces.largePadding,
                horizontal: AppSpaces.mediumPadding),
            child: Stack(
              children: [
                Column(
                  mainAxisSize: MainAxisSize.min,
                  crossAxisAlignment: CrossAxisAlignment.center,
                  children: [
                    Row(
                      children: [
                        const Icon(
                          Icons.delete,
                          color: ColorManager.errorColor,
                          size: 30,
                        ),
                        context.mediumGap,
                        Text(
                          context.tr.confirmation,
                          style: context.boldTitle,
                        ),
                      ],
                    ),

                    context.mediumGap,

                    //? Description
                    Text(
                      description,
                      style: context.subTitle,
                    ),

                    context.largeGap,

                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        //? Cancel Button
                        TextButton(
                          onPressed: () => Navigator.pop(context),
                          child:
                              Text(context.tr.cancel, style: context.subTitle),
                        ),

                        context.mediumGap,

                        TextButton(
                          onPressed: onConfirm,
                          child: Text(context.tr.confirm,
                              style: context.whiteSubTitle.copyWith(
                                  color: ColorManager.errorColor,
                                  fontWeight: FontWeight.bold)),
                        ),
                      ],
                    ).sized(
                      height: 45,
                    )
                  ],
                ),
                if (isLoading)
                  Positioned.fill(
                    child: Container(
                      alignment: Alignment.center,
                      color: Colors.white,
                      child: const LoadingWidget(
                        loadingType: LoadingType.button,
                      ),
                    ),
                  ),
              ],
            ),
          ),
        ));
  }
}

void showDeleteDialog({
  required BuildContext context,
  required String description,
  required Function() onConfirm,
}) {
  showDialog(
      context: context,
      builder: (_) => BaseDeleteDialog(
            description: description,
            onConfirm: onConfirm,
          ));
}
