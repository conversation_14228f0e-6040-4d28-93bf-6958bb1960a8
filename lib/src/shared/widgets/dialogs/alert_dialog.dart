part of shared_widgets;

class AlertDialogWidget extends ConsumerWidget {
  final Widget child;
  final String? header;
  final String iconPath;
  final String networkImage;
  final Function() onConfirm;
  final bool isLoading;
  final bool isImage;

  const AlertDialogWidget(
      {super.key,
      required this.child,
      this.isLoading = false,
      this.isImage = true,
      this.iconPath = '',
      this.networkImage = '',
      required this.onConfirm,
      this.header});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaController = ref.watch(mediaPickerControllerProvider);

    return WillPopScope(
      onWillPop: () async {
        mediaController.clearFiles();

        return true;
      },
      child: Dialog(
        surfaceTintColor: Colors.transparent,
        backgroundColor: Colors.transparent,
        insetPadding: EdgeInsets.zero,
        child: SingleChildScrollView(
          child: BaseDialog(
            radius: AppRadius.baseContainerRadius,
            backgroundColor: context.appTheme.cardColor,
            child: Stack(
              alignment: Alignment.topRight,
              children: [
                Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    //! Header text
                    Text(
                      header!,
                      style: context.boldTitle.copyWith(

                          color: const Color(0xff3A3636)),
                    ),

                    context.largeGap,
                    if (isImage)
                      SinglePickImageWidget(
                        networkImage: networkImage,
                        iconPath: iconPath,
                      ),

                    context.largeGap,

                    //! Widget
                    child,

                    context.largeGap,

                    //? Button==============================
                    Button(
                      label: context.tr.save,
                      isLoading: isLoading,
                      loadingWidget: const LoadingWidget(
                        loadingType: LoadingType.button,
                      ),
                      onPressed: () async {
                        await onConfirm();
                      },
                      color: ColorManager.buttonColor,
                    )
                  ],
                ),
                const Icon(
                  Icons.close,
                  size: 35,
                ).onTap(() {
                  context.back();


                  mediaController.clearFiles();

                }),
              ],
            ),
          ),
        ),
      ),
    );
  }
}
