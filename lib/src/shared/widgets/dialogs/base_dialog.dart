part of shared_widgets;

class BaseDialog extends StatefulWidget {
  final Widget child;
  final double radius;
  final bool isLoading;
  final Color backgroundColor;

  const BaseDialog({
    super.key,
    required this.child,
    this.isLoading = false,
    this.radius = AppRadius.baseRadius,
    this.backgroundColor = Colors.white,
  });

  @override
  State<StatefulWidget> createState() => _MainDialog();
}

class _MainDialog extends State<BaseDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController controller;

  late Animation<double> scaleAnimation;

  @override
  void initState() {
    controller = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 500));
    scaleAnimation = CurvedAnimation(
        parent: controller, curve: Curves.fastEaseInToSlowEaseOut);

    controller.addListener(() {
      setState(() {});
    });

    controller.forward();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    if (widget.isLoading) {
      return Material(
        color: Colors.transparent,
        child: ScaleTransition(
            scale: scaleAnimation,
            child: Container(
              margin: const EdgeInsets.symmetric(
                  horizontal: AppSpaces.xxLargePadding),
              decoration: ShapeDecoration(
                  color: widget.backgroundColor,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(widget.radius))),
              child: const LoadingWidget(),
            )),
      );
    }
    return Center(
      child: Material(
        color: Colors.transparent,
        child: ScaleTransition(
          scale: scaleAnimation,
          child: Container(
              margin: const EdgeInsets.all(AppSpaces.mediumPadding),
              padding: const EdgeInsets.all(AppSpaces.mediumPadding),
              decoration: ShapeDecoration(
                  color: widget.backgroundColor,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(widget.radius))),
              child: widget.child),
        ),
      ),
    );
  }
}
