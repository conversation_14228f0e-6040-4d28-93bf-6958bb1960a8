import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/services/app_settings/controller/settings_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class LanguageDialog extends ConsumerWidget {
  const LanguageDialog({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final settingsController = ref.read(settingsControllerProvider);

    return AlertDialog(
      surfaceTintColor: Colors.white,
      title: Text(
        context.tr.changeLanguage,
        style:
            context.title.copyWith(fontWeight: FontWeight.bold, fontSize: 22),
      ),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          ListTile(
            onTap: () {
              settingsController.updateLanguage(const Locale('en'));
              context.back();
            },
            title: Text(context.tr.english),
            leading: const Icon(
              Icons.language,
              color: ColorManager.primaryColor,
            ),
          ),
          ListTile(
            onTap: () {
              settingsController.updateLanguage(const Locale('ar'));
              context.back();
            },
            title: Text(context.tr.arabic),
            leading: const Icon(
              Icons.language,
              color: ColorManager.primaryColor,
            ),
          ),
        ],
      ),
    );
  }
}
