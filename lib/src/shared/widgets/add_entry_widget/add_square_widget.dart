part of shared_widgets;

class AddSquareWidget extends StatelessWidget {
  final bool isStudent;
  const AddSquareWidget({super.key, this.isStudent = false});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 100.h,
      width: 100.w,
      alignment: Alignment.center,
      child: DottedBorder(
        borderType: BorderType.RRect,
        radius: const Radius.circular(AppRadius.baseContainerRadius),
        dashPattern: const [
          5,
          5,
        ],
        color: Colors.grey,
        strokeWidth: 2,
        child: Container(
            height: isStudent ? 100.h : null,
            width: isStudent ? 100.w : null,
            alignment: Alignment.center,
            child: isStudent
                ? Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        height: 40.h,
                        child: Image.asset(
                          Assets.imagesBaby,
                          fit: BoxFit.cover,
                        ),
                      ),
                      context.smallGap,
                      Text(
                        context.tr.addNewStudents,
                        style: context.hint.copyWith(
                            fontSize: 12,
                            color: ColorManager.primaryColor,
                            fontWeight: FontWeight.w400),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  )
                : Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      SizedBox(
                        height: 30.h,
                        child: Image.asset(
                          Assets.imagesMember,
                          fit: BoxFit.cover,
                        ),
                      ),
                      context.smallGap,
                      Text(
                        context.tr.addNurseryTeamMember,
                        style: context.subTitle.copyWith(
                            color: ColorManager.primaryColor,
                            fontWeight: FontWeight.w400),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  )),
      ),
    );
  }
}
