part of shared_widgets;

class AddRectangleWidget extends StatelessWidget {
  final Function()? onTap;
  final bool isClasses;
  final String? title;

  const AddRectangleWidget({super.key, this.isClasses = true, this.onTap, this.title});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        height: 50.h,
        width: double.infinity,
        alignment: Alignment.center,
        child: DottedBorder(
          borderType: BorderType.RRect,
          radius: const Radius.circular(AppRadius.baseContainerRadius),
          dashPattern: const [
            3,
            3,
          ],
          color: Colors.grey,
          strokeWidth: 2,
          child: Container(
              height: 50.h,
              alignment: Alignment.center,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Image.asset(
                    Assets.iconsAdd,
                    color: ColorManager.primaryColor,
                    fit: BoxFit.cover,
                  ),
                  context.smallGap,
                  Text( title == null ?
                    isClasses
                        ? context.tr.addNewClass
                        : context.tr.addNurseryActivities :title!,
                    style: context.title.copyWith(
                        color: ColorManager.primaryColor,
                        fontWeight: FontWeight.w400),
                  ),
                ],
              )),
        ),
      ),
    );
  }
}
