part of shared_widgets;

class BaseCachedImage extends StatelessWidget {
  final String imageUrl;
  final double? height;
  final double? width;
  final BoxFit? fit;
  final double? radius;
  final Widget? errorWidget;

  const BaseCachedImage(
    this.imageUrl, {
    super.key,
    this.height,
    this.width,
    this.fit = BoxFit.cover,
    this.errorWidget,
    this.radius,
  });

  @override
  Widget build(BuildContext context) {
    const errorWidget = CircleAvatar(
      backgroundColor: Colors.grey,
      radius: 30,
      child: Icon(
        Icons.error,
        color: Colors.white,
      ),
    );

    return ClipRRect(
      borderRadius: BorderRadius.circular(radius ?? 0),
      child: SizedBox(
        height: height,
        width: width,
        child: FastCachedImage(
          fadeInDuration: const Duration(milliseconds: 400),
          url: imageUrl,
          height: height,
          width: width,
          fit: fit,
          loadingBuilder: (context, url) => loadingShimmerWidget(),
          errorBuilder: (context, url, error) =>
              this.errorWidget ?? errorWidget,
        ),
      ),
    );
  }

  Widget loadingShimmerWidget() => Center(
        child: Shimmer(
          gradient: LinearGradient(
            colors: [
              Colors.grey[300]!,
              Colors.grey[100]!,
            ],
          ),
          child: Container(
              height: height,
              width: width,
              decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: radius == null
                    ? null
                    : BorderRadius.all(Radius.circular(radius!)),
              )),
        ),
      );
}
