import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class BaseContainer extends StatelessWidget {
  final double radius;
  final double padding;
  final double? margin;
  final Color color;
  final Color borderColor;
  final List<BoxShadow>? boxShadow;
  final Widget child;
  final Function()? onTap;

  const BaseContainer(
      {super.key,
      this.radius = AppRadius.baseContainerRadius,
      this.color = ColorManager.secondaryColor,
      this.padding = AppSpaces.mediumPadding,
      this.borderColor = ColorManager.grey,
      this.boxShadow,
      this.margin,
      this.onTap,
      required this.child});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: double.infinity,
        padding: EdgeInsets.all(padding),
        margin: EdgeInsets.symmetric(horizontal: margin ?? 0),
        decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(radius),
            border: Border.all(color: borderColor),
            boxShadow: boxShadow),
        child: child,
      ),
    );
  }
}
