import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class BaseTextButton extends StatelessWidget {
  final String title;
  final Function() onTap;

  const BaseTextButton({super.key, required this.title, required this.onTap});

  @override
  Widget build(BuildContext context) {
    return TextButton(
      onPressed: onTap,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          const Icon(
            CupertinoIcons.checkmark_square,
            color: ColorManager.darkBlue,
            size: 18,
          ),
          Text(
            title,
            style: const TextStyle(color: ColorManager.darkBlue),
          ),
        ],
      ),
    );
  }
}
