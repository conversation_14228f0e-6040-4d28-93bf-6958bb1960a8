import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class SwitchButtonWidget extends StatelessWidget {
  final ValueNotifier<bool> value;
  final Function(bool) onChanged;
  final String? title;

  const SwitchButtonWidget(
      {super.key, required this.value, required this.onChanged, this.title});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null)
          Text(
            title!,
            style: context.greyLabelLarge,
          ),
        Switch.adaptive(
            trackOutlineColor: value.value
                ? const MaterialStatePropertyAll(Colors.transparent)
                : const MaterialStatePropertyAll(ColorManager.grey),
            trackOutlineWidth: const MaterialStatePropertyAll(1),
            hoverColor: Colors.blue,
            activeTrackColor: Colors.green,
            thumbColor: const MaterialStatePropertyAll(Colors.white),
            inactiveTrackColor: Colors.grey.withOpacity(0.5),
            value: value.value,
            onChanged: onChanged),
      ],
    );
  }
}
