import 'package:connectify_app/src/screens/teacher/controllers/teacher_controller.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class TeachersDropDown extends HookConsumerWidget {
  final ValueNotifier<List<TeacherModel>?> selectedTeachers;
  final List<int?>? teacherIds;

  const TeachersDropDown(
      {super.key, required this.selectedTeachers, this.teacherIds});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final teacherController = ref.watch(getTeacherDataProvider(context));

    var teachers = useState<List<TeacherModel>>([]);

    teacherController.whenData((data) {
      teachers.value = data;

      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        if (teacherIds != null &&
            teacherIds!.isNotEmpty &&
            selectedTeachers.value == null) {
          selectedTeachers.value = teachers.value
              .where((element) => teacherIds!.contains(element.id))
              .toList();
        }
      });
    });

    if (teacherController.isLoading) return const LinearProgressIndicator();

    return BaseSearchDropDown(
      label: context.tr.teachers,
      data: teachers.value,
      isEng: context.isEng,
      isMultiSelect: true,
      itemModelAsName: (data) => data.name,
      multiItemsAsName: (data) =>
          (data).map((e) => e.name).toList().toString().filterMultiDropDownList,
      selectedValue: selectedTeachers.value,
      onChanged: (value) {
        selectedTeachers.value = List<TeacherModel>.from(value as List);
      },
    );
  }
}
