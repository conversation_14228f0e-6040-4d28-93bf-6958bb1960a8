import 'dart:developer';

import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/class/controllers/class_controller.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class ClassDropDown extends HookConsumerWidget {
  final ValueNotifier<ClassModel?> selectedClass;
  final int? classId;

  const ClassDropDown({super.key, required this.selectedClass, this.classId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final classController = ref.watch(getClassDataProvider(context));

    var classes = useState<List<ClassModel>>([]);

    classController.whenData((data) {
      classes.value = data;

      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        if (classId != null && selectedClass.value == null) {
          selectedClass.value = classes.value
              .firstWhereOrNull((element) => element.id == classId);
        }
      });
    });

    if (classController.isLoading) return const LinearProgressIndicator();

    return BaseSearchDropDown(
      label: context.tr.className,
      data: classes.value,
      itemModelAsName: (classModel) => (classModel as ClassModel).name,
      selectedValue: selectedClass.value,
      isEng: context.isEng,
      onChanged: (value) {
        selectedClass.value = value as ClassModel?;
      },
    );
  }
}

class MultiClassDropDown extends HookConsumerWidget {
  final ValueNotifier<List<ClassModel>> selectedClasses;
  final List<int?>? classIds;

  const MultiClassDropDown(
      {super.key, required this.selectedClasses, this.classIds});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final classController = ref.watch(getClassDataProvider(context));

    var classes = useState<List<ClassModel>>([]);

    classController.whenData((data) {
      classes.value = data;
      //      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
      //         if (teacherIds != null &&
      //             teacherIds!.isNotEmpty &&
      //             selectedTeachers.value == null) {
      //           selectedTeachers.value = teachers.value
      //               .where((element) => teacherIds!.contains(element.id))
      //               .toList();
      //         }
      //       });
      //     });

      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        log('asffsafsaf ${classIds}');
        if (selectedClasses.value.isEmpty && classIds != null) {
          selectedClasses.value = classes.value
              .where((element) => classIds!.contains(element.id))
              .toList();
        }
      });
    });

    if (classController.isLoading) return const LinearProgressIndicator();

    return BaseSearchDropDown(
      label: context.tr.className,
      data: classes.value,
      isMultiSelect: true,
      isEng: context.isEng,
      multiItemsAsName: (data) =>
          (data).map((e) => e.name).toList().toString().filterMultiDropDownList,
      itemModelAsName: (classModel) => (classModel as ClassModel).name,
      selectedValue: selectedClasses.value,
      onChanged: (value) {
        selectedClasses.value = List<ClassModel>.from(value as List);
      },
    );
  }
}
