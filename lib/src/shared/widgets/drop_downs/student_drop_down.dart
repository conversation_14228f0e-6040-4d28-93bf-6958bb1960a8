import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class StudentDropDown extends HookConsumerWidget {
  final ValueNotifier<StudentModel?> selectedStudent;
  final String? label;
  final int? studentId;

  const StudentDropDown(
      {super.key, required this.selectedStudent, this.studentId, this.label});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final studentController = ref.watch(getActiveStudents(context));

    var students = useState<List<StudentModel>>([]);

    studentController.whenData((data) {
      students.value = data.where((element) => element.id != 0).toList();

      WidgetsBinding.instance.addPostFrameCallback((timeStamp) {
        if (studentId != null && selectedStudent.value == null) {
          selectedStudent.value = students.value
              .firstWhereOrNull((element) => element.id == studentId);
        }
      });
    });

    if (studentController.isLoading) return const LinearProgressIndicator();

    return BaseSearchDropDown(
      label: label ?? context.tr.studentName,
      data: students.value,
      itemModelAsName: (studentModel) => (studentModel as StudentModel).name,
      selectedValue: selectedStudent.value,
      isEng: context.isEng,
      onChanged: (value) {
        selectedStudent.value = value as StudentModel?;
      },
    );
  }
}
