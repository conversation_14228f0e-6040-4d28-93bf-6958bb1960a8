import 'dart:io';

import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:video_player/video_player.dart';
import 'package:xr_helper/xr_helper.dart';

class VideoPlayerScreen extends HookWidget {
  final String videoUrl;
  final bool isNetworkVideo;

  const VideoPlayerScreen({
    super.key,
    required this.videoUrl,
    this.isNetworkVideo = true,
  });

  @override
  Widget build(BuildContext context) {
    final controller = useMemoized(() {
      return isNetworkVideo
          ? VideoPlayerController.networkUrl(Uri.parse(videoUrl))
          : VideoPlayerController.file(File(videoUrl));
    }, [videoUrl]);

    final isInitialized = useState(false);
    final isPlaying = useState(false);
    final showControls = useState(true);

    useEffect(() {
      controller.initialize().then((_) {
        isInitialized.value = true;
      });

      controller.addListener(() {
        if (controller.value.isPlaying != isPlaying.value) {
          isPlaying.value = controller.value.isPlaying;
        }
      });

      return () {
        controller.dispose();
      };
    }, [controller]);

    // Auto-hide controls after 3 seconds
    useEffect(() {
      if (showControls.value && isPlaying.value) {
        Future.delayed(const Duration(seconds: 3), () {
          if (isPlaying.value) {
            showControls.value = false;
          }
        });
      }
      return null;
    }, [showControls.value, isPlaying.value]);

    return Scaffold(
      backgroundColor: Colors.black,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back, color: Colors.white),
          onPressed: () => Navigator.of(context).pop(),
        ),
      ),
      body: Center(
        child: isInitialized.value
            ? GestureDetector(
                onTap: () {
                  showControls.value = !showControls.value;
                },
                child: Stack(
                  alignment: Alignment.center,
                  children: [
                    AspectRatio(
                      aspectRatio: controller.value.aspectRatio,
                      child: VideoPlayer(controller),
                    ),
                    if (showControls.value)
                      Container(
                        color: Colors.black26,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            const Spacer(
                              flex: 2,
                            ),
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                IconButton(
                                  onPressed: () {
                                    final currentPosition =
                                        controller.value.position;
                                    final newPosition = currentPosition -
                                        const Duration(seconds: 10);
                                    controller.seekTo(newPosition);
                                  },
                                  icon: const Icon(
                                    Icons.replay_10,
                                    color: Colors.white,
                                    size: 32,
                                  ),
                                ),
                                context.mediumGap,
                                IconButton(
                                  onPressed: () {
                                    if (controller.value.isPlaying) {
                                      controller.pause();
                                    } else {
                                      controller.play();
                                    }
                                  },
                                  icon: Icon(
                                    controller.value.isPlaying
                                        ? Icons.pause_circle_filled
                                        : Icons.play_circle_filled,
                                    color: Colors.white,
                                    size: 64,
                                  ),
                                ),
                                context.mediumGap,
                                IconButton(
                                  onPressed: () {
                                    final currentPosition =
                                        controller.value.position;
                                    final newPosition = currentPosition +
                                        const Duration(seconds: 10);
                                    controller.seekTo(newPosition);
                                  },
                                  icon: const Icon(
                                    Icons.forward_10,
                                    color: Colors.white,
                                    size: 32,
                                  ),
                                ),
                              ],
                            ),
                            const Spacer(),
                            context.xlLargeGap,
                            Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 20),
                              child: VideoProgressIndicator(
                                controller,
                                allowScrubbing: true,
                                colors: const VideoProgressColors(
                                  playedColor: Colors.blue,
                                  bufferedColor: Colors.grey,
                                  backgroundColor: Colors.white24,
                                ),
                              ),
                            ),
                            context.smallGap,
                            Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Padding(
                                  padding: const EdgeInsets.only(left: 20),
                                  child: Text(
                                    _formatDuration(controller.value.position),
                                    style: const TextStyle(color: Colors.white),
                                  ),
                                ),
                                Padding(
                                  padding: const EdgeInsets.only(right: 20),
                                  child: Text(
                                    _formatDuration(controller.value.duration),
                                    style: const TextStyle(color: Colors.white),
                                  ),
                                ),
                              ],
                            ),
                            context.xxLargeGap,
                          ],
                        ),
                      ),
                  ],
                ),
              )
            : const CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
      ),
    );
  }

  String _formatDuration(Duration duration) {
    String twoDigits(int n) => n.toString().padLeft(2, '0');
    final hours = twoDigits(duration.inHours);
    final minutes = twoDigits(duration.inMinutes.remainder(60));
    final seconds = twoDigits(duration.inSeconds.remainder(60));

    if (duration.inHours > 0) {
      return '$hours:$minutes:$seconds';
    } else {
      return '$minutes:$seconds';
    }
  }
}
