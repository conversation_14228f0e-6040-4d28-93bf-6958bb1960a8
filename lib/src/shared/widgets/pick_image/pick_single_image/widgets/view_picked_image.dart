part of shared_widgets;

class _ViewPickedImage extends StatelessWidget {
  final String? pickedResult;

  const _ViewPickedImage({required this.pickedResult});

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        //! Picked Image
        Container(
          height: 120,
          width: 150,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
          ),
          child: ClipRRect(
            borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
            child: Image.file(
              File(pickedResult!),
              fit: BoxFit.cover,
            ),
          ),
        ),

        //! Edit button
        const _EditButton(),
      ],
    );
  }
}
