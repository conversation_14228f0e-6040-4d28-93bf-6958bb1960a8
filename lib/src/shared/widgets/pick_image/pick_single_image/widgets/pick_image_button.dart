part of shared_widgets;

class _PickImageButton extends ConsumerWidget {
  final String? iconPath;

  const _PickImageButton({this.iconPath = ''});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Stack(
      alignment: Alignment.bottomRight,
      children: [
        Container(
            height: 120,
            width: 150,
            alignment: Alignment.center,
            decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(
                    color: ColorManager.primaryColor.withOpacity(0.5),
                    width: 4.w)),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Image.asset(Assets.imagesActivities),
              ],
            )),
        Positioned(
            right: 20.w,
            child: const CircleAvatar(
                backgroundColor: ColorManager.black,
                radius: 15,
                child: Icon(
                  Icons.add,
                  color: ColorManager.white,
                ))),
      ],
    );
  }
}
