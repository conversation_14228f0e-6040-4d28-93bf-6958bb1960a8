part of shared_widgets;

class SinglePickImageWidget extends ConsumerWidget {
  final String? networkImage;
  final String? iconPath;
  final Widget? pickImageWidget;

  const SinglePickImageWidget({
    super.key,
    this.networkImage,
    this.iconPath,
    this.pickImageWidget,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaController = ref.watch(mediaPickerControllerProvider);

    final pickedResult = mediaController.filePath;

    final showPickImageButton =
        pickedResult.isEmpty && (networkImage == null || networkImage!.isEmpty);

    void onPickImage() => mediaController.pickFile();

    //! Pick Image Button ========================================
    if (showPickImageButton) {
      if (pickImageWidget != null) {
        return InkWell(onTap: onPickImage, child: pickImageWidget!);
      }

      return InkWell(
        onTap: onPickImage,
        child: _PickImageButton(
          iconPath: iconPath,
        ),
      );
    }

    final pickResultIsNotNull = pickedResult.isNotEmpty;

    //! View Picked Image ========================================
    if (pickResultIsNotNull) {
      return _ViewPickedImage(
        pickedResult: pickedResult,
      );
    }

    //! View Network Image ========================================
    return _ViewNetworkImage(
      viewedNetworkImage: networkImage!,
    );
  }
}
