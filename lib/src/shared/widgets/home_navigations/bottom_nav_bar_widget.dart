import 'dart:developer';

import 'package:auto_height_grid_view/auto_height_grid_view.dart';
import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/home/<USER>/bottom_nav_controller.dart';
import 'package:connectify_app/src/screens/messages/controller/messages_controller.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/icon_widget/icon_widget.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../screens/home/<USER>/home_add_model.dart';
import '../../../screens/home/<USER>/main_screen/widgets/floating_button.dart';

ValueNotifier<int?> bottomNavMessageCountValue = ValueNotifier<int?>(null);

class BottomNavBarWidget extends ConsumerWidget {
  const BottomNavBarWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(bottomNavigationControllerProvider);
    final bottomNavCtrl = ref.read(bottomNavController);

    final localMessageCount = GetStorageService.getLocalData(
      key: LocalKeys.messageCount,
    );

    final messageCount = ref.watch(getAllMessageData(context)).when(
      data: (data) {
        final nurseryMessages =
            data.where((element) => element.type != 'parent').toList();
        final onlineMessageCount = nurseryMessages.length;
        final newMessages = onlineMessageCount -
            (int.tryParse(localMessageCount.toString()) ?? 0);
        bottomNavMessageCountValue.value = newMessages > 0 ? newMessages : 0;
        return bottomNavMessageCountValue.value!;
      },
      error: (error, stackTrace) {
        return 0;
      },
      loading: () {
        return 0;
      },
    );

    log('Local message count: $localMessageCount');
    log('Message count: $messageCount');

    final index0 = currentIndex == 0;
    final index1 = currentIndex == 1;
    final index3 = currentIndex == 3;
    final index4 = currentIndex == 4;

    return Container(
      height: 60.h,
      decoration: BoxDecoration(
        color: ColorManager.secondaryColor,
        borderRadius: const BorderRadius.only(
          topRight: Radius.circular(AppRadius.bottomNavBarRadius),
          topLeft: Radius.circular(AppRadius.bottomNavBarRadius),
        ),
        boxShadow: ConstantsWidgets.boxShadow,
      ),
      child: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        unselectedItemColor: ColorManager.bottomNavIconColor,
        selectedItemColor: ColorManager.primaryColor,
        selectedLabelStyle: const TextStyle(
            color: ColorManager.primaryColor, fontWeight: FontWeight.bold),
        unselectedLabelStyle: const TextStyle(color: ColorManager.primaryColor),
        selectedFontSize: index1 || index4 ? 10 : 12,
        unselectedFontSize: 9,
        elevation: 0,
        backgroundColor: Colors.transparent,
        currentIndex: currentIndex,
        onTap: (index) {
          if (index == 2) return;
          if (index == 3) {
            GetStorageService.setLocalData(
              key: LocalKeys.messageCount,
              value: ref.read(getAllMessageData(context)).when(
                    data: (data) => data
                        .where((element) => element.type != 'parent')
                        .length,
                    error: (error, stackTrace) => 0,
                    loading: () => 0,
                  ),
            );
            bottomNavMessageCountValue.value = 0;
          }
          if (index == 4) {
            showSettings(context);
            return;
          }
          bottomNavCtrl.changeIndex(index);
        },
        items: [
          BottomNavigationBarItem(
            icon: IconWidget(
              icon: Assets.svgHome,
              color: index0
                  ? ColorManager.primaryColor
                  : ColorManager.bottomNavIconColor,
            ),
            label: context.tr.home,
          ),
          BottomNavigationBarItem(
            icon: IconWidget(
              icon: Assets.svgSupplies,
              color: index1
                  ? ColorManager.primaryColor
                  : ColorManager.bottomNavIconColor,
            ),
            label: context.tr.supplies,
          ),
          const BottomNavigationBarItem(
            icon: SizedBox.shrink(),
            label: '',
          ),
          BottomNavigationBarItem(
            icon: Stack(
              children: [
                Badge.count(
                  isLabelVisible: messageCount > 0,
                  backgroundColor: ColorManager.buttonColor,
                  count: messageCount,
                  child: IconWidget(
                    icon: Assets.svgMessagesParent,
                    color: index3
                        ? ColorManager.primaryColor
                        : ColorManager.bottomNavIconColor,
                  ),
                ),
              ],
            ),
            label: context.tr.messages,
          ),
          BottomNavigationBarItem(
            icon: IconWidget(
              icon: Assets.svgMoreParent,
              color: index4
                  ? ColorManager.primaryColor
                  : ColorManager.bottomNavIconColor,
            ),
            label: context.tr.more,
          ),
        ],
      ),
    );
  }

  Future<void> showSettings(BuildContext context) async {
    return showModalBottomSheet(
      elevation: 0,
      isScrollControlled: true,
      context: context,
      builder: (BuildContext context) {
        return Consumer(
          builder: (context, ref, child) {
            final addList = HomeAddModel.homeList(context, ref);
            final addList2 = HomeAddModel.homeList2(context);

            return SizedBox(
              height: 260,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  IconButton(
                      onPressed: () => context.back(),
                      icon: const Icon(
                        Icons.close,
                        size: 30,
                      )),
                  Expanded(
                    child: Align(
                      alignment: Alignment.bottomCenter,
                      child: AutoHeightGridView(
                          padding: EdgeInsets.zero,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: addList.length,
                          crossAxisCount: 3,
                          builder: (BuildContext context, int index) {
                            return Consumer(
                              builder: (context, ref, child) {
                                final bottomNavBarAddWidget =
                                    ref.watch(bottomNavController);
                                return BottomNavBarAddWidget(
                                  onTap: index == 1
                                      ? () {
                                          bottomNavBarAddWidget.changeIndex(1);
                                          context.back();
                                        }
                                      : addList[index].onTap,
                                  home: addList[index],
                                  isLast: index != addList.length - 1,
                                );
                              },
                            );
                          }),
                    ),
                  ),
                  const MyDivider(),
                  context.smallGap,
                  Expanded(
                    child: Align(
                      alignment: Alignment.topCenter,
                      child: AutoHeightGridView(
                          padding: EdgeInsets.zero,
                          physics: const NeverScrollableScrollPhysics(),
                          itemCount: addList2.length,
                          crossAxisCount: 3,
                          builder: (BuildContext context, int index) {
                            return BottomNavBarAddWidget(
                              onTap: addList2[index].onTap,
                              home: addList2[index],
                              isLast: index != addList2.length - 1,
                            );
                          }),
                    ),
                  ),
                ],
              )
                  .paddingAll(AppSpaces.mediumPadding)
                  .sized(height: context.height * 0.25),
            );
          },
        );
      },
    );
  }
}
