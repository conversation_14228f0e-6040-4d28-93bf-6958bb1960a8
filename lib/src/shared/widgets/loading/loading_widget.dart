import 'package:connectify_app/generated/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:lottie/lottie.dart';

enum LoadingType { linear, circular, button }

class LoadingWidget extends StatelessWidget {
  final LoadingType loadingType;

  const LoadingWidget({
    super.key,
    this.loadingType = LoadingType.circular,
  });

  @override
  Widget build(BuildContext context) {
    final isButton = loadingType == LoadingType.button;

    if (loadingType == LoadingType.linear) {
      return const LinearProgressIndicator();
    }

    return Center(
      child: Lottie.asset(Assets.animatedLoading,
          height: isButton ? 75.h : 100.h,
          width: isButton ? 75.w : 100.w,
          fit: BoxFit.cover),
    );
  }
}
