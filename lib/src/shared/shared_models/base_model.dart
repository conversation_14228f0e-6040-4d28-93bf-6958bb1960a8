import 'package:connectify_app/src/shared/shared_models/base_media_model.dart';
import 'package:equatable/equatable.dart';

class BaseModel extends Equatable {
  final int? id;
  final String name;
  final String description;
  final BaseMediaModel? image;
  final String? createdAt;

  const BaseModel({
    this.id,
    this.name = '',
    this.description = '',
    this.image,
    this.createdAt,
  });

  @override
  List<Object?> get props => [id, name, description, image, createdAt];
}
