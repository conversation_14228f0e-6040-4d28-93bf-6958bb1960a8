import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:permission_handler/permission_handler.dart';
import 'package:xr_helper/xr_helper.dart';

final localMediaRepoProvider = Provider<LocalMediaRepo>((ref) {
  return LocalMediaRepo();
});

class LocalMediaRepo {
  Future<FilePickerResult?> pickFiles(
    {
    bool imageUpload = true,
    bool uploadMultiple = true,
  }) async {
    try {
      await _getPermission();

      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: imageUpload ? FileType.image : FileType.any,
        allowMultiple: uploadMultiple,
      );

      return result;
    } catch (e) {
      Log.e('error $e');
      return null;
    }
  }

  Future<void> _getPermission() async {
    if (await Permission.storage.isGranted) return;
    try {
      await Permission.storage.request();
    } catch (e) {
      Log.e('error $e');
    }
  }
}
