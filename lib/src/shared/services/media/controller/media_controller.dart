import 'dart:async';

import 'package:file_picker/file_picker.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../repository/local_media_repo.dart';

final mediaPickerControllerProvider =
    ChangeNotifierProvider<MediaPickerController>(
  (ref) {
    final mediaLocalRepoProvider = ref.watch(localMediaRepoProvider);

    return MediaPickerController(mediaLocalRepoProvider);
  },
);

class MediaPickerController extends ChangeNotifier {
  final LocalMediaRepo _mediaRepo;

  MediaPickerController(this._mediaRepo);

  FilePickerResult? _filePickerResult;

  List<String> get filesPaths =>
      _filePickerResult?.paths.map((e) => e ?? '').toList() ?? [];

  String get filePath => _filePickerResult?.files.firstOrNull?.path ?? '';

  Future<FilePickerResult?> pickFile(
   {
    bool imageUpload = true,
    bool allowMultiple = false,
  }) async {
    try {
      final pickedFiles = await _mediaRepo.pickFiles(
        imageUpload: imageUpload,
        uploadMultiple: allowMultiple,
      );

      if (pickedFiles == null) return null;

      _filePickerResult = pickedFiles;

      notifyListeners();

      return pickedFiles;
    } on Exception catch (e) {
      Log.e('Error Getting File $e');
      rethrow;
    }
  }

  void clearFiles() {
    _filePickerResult = null;

    Log.i('FileClearedSuccessfully');

    notifyListeners();
  }

  void removeFile(int index) {
    _filePickerResult?.files.removeAt(index);
    notifyListeners();
  }
}
