import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

final settingsRepoProvider = Provider<SettingsLocalRepo>((ref) {
  return SettingsLocalRepo();
});

class SettingsLocalRepo {
  Future<void> updateLanguage(Locale locale) async {
    GetStorageService.setLocalData(
        key: LocalKeys.language, value: locale.languageCode);
  }

  Future<Locale> locale() async {
    final langCode =
        await GetStorageService.getLocalData(key: LocalKeys.language);
    if (langCode != null) {
      return Locale(langCode);
    } else {
      return const Locale('en');
    }
  }
}
