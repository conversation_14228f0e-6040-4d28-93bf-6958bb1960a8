import 'package:connectify_app/src/screens/sleep/model/sleep_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

// * =========================================================

final sleepRepoProvider = Provider((ref) {
  final networkApiServices = ref.watch(networkServiceProvider);

  return SleepRepo(networkApiServices);
});

//? ========================================================

class SleepRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  SleepRepo(this._networkApiServices);

//? get Sleep Data ========================================================
  Future<List<SleepModel>> getSleepData() async {
    return await baseFunction(() async {
      final response =
          await _networkApiServices.getResponse(ApiEndpoints.sleep);

      final sleepData = await compute(responseToSleepModelList, response);

      return sleepData;
    });
  }

//? Add Sleep ========================================================

  Future<void> addSleep({required SleepModel sleep}) async {
    return await baseFunction(() async {
      await _networkApiServices.postResponse(ApiEndpoints.editDeleteSleeps,
          body: sleep.toJson());
    });
  }

  //getSleepDataByDay
  Future<List<SleepModel>> getSleepDataByDay({required DateTime date}) async {
    return await baseFunction(() async {
      final response = await _networkApiServices.getResponse(
          '${ApiEndpoints.sleep}&${ApiEndpoints.filterByDate(date)}');

      final sleepData = await compute(responseToSleepModelList, response);

      return sleepData;
    });
  }
}
