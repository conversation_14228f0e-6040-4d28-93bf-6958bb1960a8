import 'package:connectify_app/src/screens/sleep/controller/sleep_controller.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../shared/widgets/shared_widgets.dart';
import 'add_sleep_widgets.dart';

class AddSleepDialog extends StatelessWidget {
  const AddSleepDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox();
  }
}

Future<void> showAddSleepDialog(context) {
  return showDialog(
    context: context,
    builder: (context) {
      return HookConsumer(
        builder: (context, ref, child) {
          final sleepCtrl =
              ref.watch(sleepChangeNotifierControllerProvider(context));
          //!-----------------------------------------------------

          final formKey = useState(GlobalKey<FormState>());

          //!-----------------------------------------------------

          final studentValue = useState<StudentModel?>(null);

          final controllers = {
            ApiStrings.from: useTextEditingController(),
            ApiStrings.to: useTextEditingController(),
          };
          //!-----------------------------------------------------

          Future<void> addSleep() async {
            if (!formKey.value.currentState!.validate()) return;
            await sleepCtrl.addSleep(
                controllers: controllers, student: studentValue.value);
          }

          return AlertDialogWidget(
            header: context.tr.sleep,
            isLoading: sleepCtrl.isLoading,
            isImage: false,
            child: Form(
              key: formKey.value,
              child: AddSleepWidgets(
                studentValue: studentValue,
                controllers: controllers,
              ),
            ),
            onConfirm: () async
            => await addSleep(),
          );
        },
      );
    },
  );
}
