import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/widgets/drop_downs/student_drop_down.dart';

class AddSleepWidgets extends StatelessWidget {
  final ValueNotifier studentValue;
  final Map<String, TextEditingController> controllers;

  const AddSleepWidgets(
      {super.key, required this.studentValue, required this.controllers});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        StudentDropDown(
          selectedStudent: studentValue as ValueNotifier<StudentModel?>,
        ),
        context.mediumGap,
        Row(
          children: [
            Expanded(
                child: GestureDetector(
              onTap: () async {
                final data = await showTimePickerDialog(
                  context,
                );

                if (data != null) {
                  controllers[ApiStrings.from]?.text = data;
                }
              },
              child: BaseTextField(
                title: context.tr.from,
                controller: controllers[ApiStrings.from],
                enabled: false,
                hint: '00:00',
              ),
            )),
            context.smallGap,
            Expanded(
                child: GestureDetector(
              onTap: () async {
                final data = await showTimePickerDialog(
                  context,
                );

                if (data != null) {
                  controllers[ApiStrings.to]?.text = data;
                }
              },
              child: BaseTextField(
                title: context.tr.to,
                controller: controllers[ApiStrings.to],
                enabled: false,
                hint: '00:00',
              ),
            )),
          ],
        )
      ],
    );
  }
}

Future<String?> showTimePickerDialog(
  BuildContext context,
) async {
  try {
    final timeOfDay = await showTimePicker(
      context: context,
      initialTime: TimeOfDay.now(),
    );

    if (timeOfDay == null) return null;

    return '${timeOfDay.hour}:${timeOfDay.minute}';
    // return '${timeOfDay.hourOfPeriod}:${timeOfDay.minute} ${timeOfDay.period.name}';
  } catch (e) {
    return null;
  }
}
