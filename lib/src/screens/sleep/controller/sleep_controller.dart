import 'package:connectify_app/src/screens/sleep/repo/sleep_repo.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../model/sleep_model.dart';

final sleepControllerProvider =
    Provider.family<SleepController, BuildContext>((ref, context) {
  final sleepRepo = ref.watch(sleepRepoProvider);

  return SleepController(sleepRepo: sleepRepo, context: context);
});
final sleepChangeNotifierControllerProvider =
    ChangeNotifierProvider.family<SleepController, BuildContext>(
        (ref, context) {
  final sleepRepo = ref.watch(sleepRepoProvider);

  return SleepController(sleepRepo: sleepRepo, context: context);
});

//getSleepByDayData
final getSleepByDayData =
    FutureProvider.family<List<SleepModel>, (BuildContext, DateTime)>(
        (ref, params) {
  final sleepRepo = ref.watch(sleepRepoProvider);
  final context = params.$1;

  final sleepController =
      SleepController(sleepRepo: sleepRepo, context: context);

  final date = params.$2;

  return sleepController.getSleepDataByDay(date: date);
});

final getAllSleepData =
    FutureProvider.family<List<SleepModel>, BuildContext>((ref, context) {
  final sleepRepo = ref.watch(sleepRepoProvider);

  final sleepController =
      SleepController(sleepRepo: sleepRepo, context: context);

  return sleepController.getSleepData();
});

class SleepController extends BaseVM {
  final SleepRepo sleepRepo;
  final BuildContext context;

  SleepController({required this.sleepRepo, required this.context});

  Future<List<SleepModel>> getSleepData() async {
    return await baseFunction(context, () async {
      final sleepData = await sleepRepo.getSleepData();

      return sleepData;
    });
  }

  //? Add Sleep ========================================================
  Future<void> addSleep(
      {required Map<String, TextEditingController> controllers,
      required final StudentModel? student}) async {
    return await baseFunction(context, () async {
      final sleep = SleepModel(
          sleepStartTime: controllers[ApiStrings.from]?.text ?? '',
          sleepEndTime: controllers[ApiStrings.to]?.text ?? '',
          student: student);

      await sleepRepo.addSleep(sleep: sleep);

      if (!context.mounted) return;
      context.back();
      context.showBarMessage(context.tr.addedSuccessfully);
    });
  }

  //getSleepDataByDay
  Future<List<SleepModel>> getSleepDataByDay({required DateTime date}) async {
    return await baseFunction(context, () async {
      final sleepData = await sleepRepo.getSleepDataByDay(
        date: date,
      );

      return sleepData;
    });
  }
}
