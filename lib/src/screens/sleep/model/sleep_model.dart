import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:equatable/equatable.dart';

List<SleepModel> responseToSleepModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final sleepData = data.map((e) => SleepModel.fromJson(e)).toList();

  return sleepData;
}

class SleepModel extends Equatable {
  final int? id;
  final DateTime? createdAt;
  final String sleepStartTime;
  final String sleepEndTime;
  final StudentModel? student;
  final NurseryModel? nursery;
  final TeacherModel? teacher;

  const SleepModel(
      {this.id,
      this.sleepStartTime = '',
      this.sleepEndTime = '',
      this.student,
      this.createdAt,
      this.nursery,
      this.teacher});

  factory SleepModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];
    return SleepModel(
      id: json[ApiStrings.id],
      createdAt: DateTime.parse(attributes[ApiStrings.createdAt]).toLocal(),
      sleepStartTime: attributes[ApiStrings.from] ?? '',
      sleepEndTime: attributes[ApiStrings.to] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      ApiStrings.from: sleepStartTime,
      ApiStrings.to: sleepEndTime,
      ApiStrings.student: student?.id,
      ApiStrings.teacher: const UserModel().currentUser.id,
      ApiStrings.nursery: NurseryModel.currentNurseryId()
    };
  }

  @override
  List<Object?> get props =>
      [id, sleepStartTime, sleepEndTime, student, nursery, teacher];
}
