import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/supplies/model/supply_model.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/shared_models/base_model.dart';

class TeacherSupplyModel extends BaseModel {
  final List<SupplyModel>? supplies;
  final StudentModel? student;
  final TeacherModel? teacher;
  bool? markAsSent;

  TeacherSupplyModel(
      {this.supplies, super.id, this.student, this.teacher, this.markAsSent});

  factory TeacherSupplyModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];

    return TeacherSupplyModel(
      id: json[ApiStrings.id],
      markAsSent: attributes[ApiStrings.markAsSent] ?? false,
      supplies: attributes[ApiStrings.supplies] == null
          ? <SupplyModel>[]
          : attributes[ApiStrings.supplies] is List
              ? attributes[ApiStrings.supplies]
                  .map((e) => SupplyModel.fromJsonWithoutAttributes(e))
                  .toList()
              : attributes[ApiStrings.supplies].containsKey(ApiStrings.data) &&
                      attributes[ApiStrings.supplies][ApiStrings.data] != null
                  ? List.from(attributes[ApiStrings.supplies][ApiStrings.data])
                      .map((e) => SupplyModel.fromJson(e))
                      .toList()
                  : <SupplyModel>[],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      if (supplies != null)
        ApiStrings.supplies: supplies?.map((e) => e.id).toList(),
      if (student != null) ApiStrings.student: student?.id,
      ApiStrings.teacher: const UserModel().currentUser.id,
      ApiStrings.nursery: NurseryModel.currentNurseryId(),
      ApiStrings.markAsSent: markAsSent
    };
  }
}
