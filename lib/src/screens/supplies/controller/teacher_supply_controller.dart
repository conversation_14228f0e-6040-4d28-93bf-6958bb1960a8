import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/supplies/model/teacher_supply_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/widgets.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../model/supply_model.dart';
import '../repo/teacher_supply_repo.dart';

final teacherSupplyControllerProvider =
    Provider.family<TeacherSupplyController, BuildContext>((ref, context) {
  final teacherSupplyRepo = ref.watch(teacherSupplyRepoProvider);

  return TeacherSupplyController(
      teacherSupplyRepo: teacherSupplyRepo, context: context);
});

final teacherSupplyControllerChangeNotifierProvider =
    ChangeNotifierProvider.family<TeacherSupplyController, BuildContext>(
        (ref, context) {
  final teacherSupplyRepo = ref.watch(teacherSupplyRepoProvider);

  return TeacherSupplyController(
      teacherSupplyRepo: teacherSupplyRepo, context: context);
});

final getTeacherSupplyDataProvider =
    FutureProvider.family<List<TeacherSupplyModel>, BuildContext>(
        (ref, context) async {
  final supplyController = ref.watch(teacherSupplyRepoProvider);

  return await supplyController.getTeacherSupplyData();
});

class TeacherSupplyController extends BaseVM {
  final TeacherSupplyRepo teacherSupplyRepo;
  final BuildContext context;

  TeacherSupplyController(
      {required this.teacherSupplyRepo, required this.context});

  Future<void> addTeacherSupply(
      {required List<SupplyModel>? supplies,
      required StudentModel student}) async {
    return await baseFunction(context, () async {
      final teacherSupply =
          TeacherSupplyModel(student: student, supplies: supplies);

      await teacherSupplyRepo.addSupply(teacherSupply: teacherSupply);
      if (!context.mounted) return;
      context.back();
      context.toReplacement(const MainScreen());
      context.showBarMessage(context.tr.addedSuccessfully);
    });
  }

  // edit teacher supply
  Future<void> editTeacherSupply(
      {required Widget navigationWidget, required int id}) async {
    return await baseFunction(context, () async {
      final teacherSupply = TeacherSupplyModel(markAsSent: true);

      teacherSupplyRepo.editSupply(teacherSupply: teacherSupply, id: id);
    });
  }
}
