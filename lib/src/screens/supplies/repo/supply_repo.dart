import 'package:connectify_app/src/screens/supplies/model/supply_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

final supplyRepoProvider = Provider((ref) {
  final networkApiServices = ref.watch(networkServiceProvider);

  return SupplyRepo(networkApiServices);
});

class SupplyRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  SupplyRepo(this._networkApiServices);

  Future<List<SupplyModel>> getSuppliesData() async {
    return await baseFunction(() async {
      final response =
          await _networkApiServices.getResponse(ApiEndpoints.supply);

      final suppliesData =
          List.from(response[ApiStrings.data]).map((e) => SupplyModel.fromJson(e)).toList();

      return suppliesData;
    });
  }

  Future<void> addSupply({required SupplyModel supply}) async {
    return await baseFunction(() async {
      return await _networkApiServices.postResponse(ApiEndpoints.supply,
          body: supply.toJson());
    });
  }




  Future<void> editSupply({required SupplyModel supply}) async {
    return await baseFunction(() async { 
      return await _networkApiServices.putResponse('${ApiEndpoints.editDeleteSupply}/${supply.id}',
          data: supply.toJson());
    });
  }

  Future<void> deleteSupply({required int id}) async {
    return await baseFunction(() async {
      return await _networkApiServices.deleteResponse(
        '${ApiEndpoints.editDeleteSupply}/$id',
      );
    });
  }
}
