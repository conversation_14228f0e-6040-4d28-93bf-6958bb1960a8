import 'package:connectify_app/src/screens/supplies/model/teacher_supply_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../shared/data/remote/api_strings.dart';

final teacherSupplyRepoProvider = Provider((ref) {
  final networkApiServices = ref.watch(networkServiceProvider);

  return TeacherSupplyRepo(networkApiServices);
});

class TeacherSupplyRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  TeacherSupplyRepo(this._networkApiServices);

  // get Teacher Supply Data
  Future<List<TeacherSupplyModel>> getTeacherSupplyData() async {
    return await baseFunction(() async {
      final response =
          await _networkApiServices.getResponse(ApiEndpoints.teacherSupply);

      final teacherSupplyData = List.from(response[ApiStrings.data])
          .map((e) => TeacherSupplyModel.fromJson(e))
          .toList();

      return teacherSupplyData;
    });
  }

  Future<void> addSupply({required TeacherSupplyModel teacherSupply}) async {
    return await baseFunction(() async {
      return await _networkApiServices.postResponse(
          ApiEndpoints.editDeleteTeacherSupply,
          body: teacherSupply.toJson());
    });
  }

  Future<void> editSupply(
      {required TeacherSupplyModel teacherSupply, required int id}) async {
    return await baseFunction(() async {
      return await _networkApiServices.putResponse(
          '${ApiEndpoints.editDeleteTeacherSupply}/$id',
          data: teacherSupply.toJson());
    });
  }
}
