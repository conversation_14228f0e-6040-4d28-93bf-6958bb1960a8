import 'package:connectify_app/src/screens/supplies/controller/teacher_supply_controller.dart';
import 'package:connectify_app/src/screens/supplies/model/teacher_supply_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/text_button/text_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../home/<USER>/main_screen.dart';

class SupplyCard extends ConsumerWidget {
  final TeacherSupplyModel supply;

  const SupplyCard({super.key, required this.supply});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final suppliesCtrlProvider =
        ref.watch(teacherSupplyControllerChangeNotifierProvider(context));

    final isSent = supply.markAsSent ?? false;
    Log.w('supply.markAsSent ${supply.markAsSent}');
    return BaseContainer(
      padding: 0,
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                  supply.supplies
                          ?.map(
                            (e) => e.name,
                          )
                          .join(' -\n') ??
                      '',
                  style: context.title),
              if (!isSent)
                Row(
                  children: [
                    Text(context.tr.required, style: context.title),
                    context.smallGap,
                    CircleAvatar(
                      radius: 5.r,
                      backgroundColor: Colors.red,
                    ),
                  ],
                )
            ],
          ).paddingAll(AppSpaces.mediumPadding),
          if (isSent)
            Text(
              context.tr.sent,
              style: context.title.copyWith(color: ColorManager.successColor),
            ).paddingAll(AppSpaces.mediumPadding)
          else
            Container(
              decoration: const BoxDecoration(
                  color: ColorManager.lightGrey,
                  borderRadius: BorderRadius.only(
                    bottomLeft: Radius.circular(AppRadius.baseContainerRadius),
                    bottomRight: Radius.circular(AppRadius.baseContainerRadius),
                  )),
              child: BaseTextButton(
                  title: context.tr.markAsSent,
                  onTap: () async {
                    supply.markAsSent = true;

                    await suppliesCtrlProvider.editTeacherSupply(
                        navigationWidget: const MainScreen(), id: supply.id!);
                  }),
            )
        ],
      ),
    );
  }
}
