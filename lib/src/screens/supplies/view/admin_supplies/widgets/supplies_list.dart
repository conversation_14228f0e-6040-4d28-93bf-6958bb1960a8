import 'package:connectify_app/src/screens/supplies/controller/teacher_supply_controller.dart';
import 'package:connectify_app/src/screens/supplies/view/admin_supplies/widgets/supply_card.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class SuppliesList extends ConsumerWidget {
  const SuppliesList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final supplyCtrl = ref.watch(getTeacherSupplyDataProvider(context));

    return supplyCtrl.get(
      error: (error, s) {
        Log.e(error.toString() + s.toString());

        return Center(child: Text(error.toString()));
      },
      data: (supplies) {
        // final isAllSuppliesContainsMarkedAsSent =
        //     supplies.every((element) => element.markAsSent ?? false);

        if (supplies.isEmpty) {
          return Text(
            context.tr.noSupplies,
            style: context.subHeadLine,
          ).center();
        }

        return ListView.separated(
            padding: const EdgeInsets.all(AppSpaces.mediumPadding),
            itemBuilder: (context, index) => SupplyCard(
                  supply: supplies[index],
                ),
            separatorBuilder: (context, index) => context.mediumGap,
            itemCount: supplies.length);
      },
    );
  }
}
