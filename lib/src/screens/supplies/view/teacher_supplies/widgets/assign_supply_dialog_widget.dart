import 'package:connectify_app/src/screens/supplies/controller/supply_controller.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/widgets/drop_downs/student_drop_down.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import '../../../../student/models/student_model.dart';
import '../../../model/supply_model.dart';

class AssignSupplyDialogWidget extends HookConsumerWidget {
  final ValueNotifier<List<SupplyModel>> suppliesValue;
  final ValueNotifier<StudentModel?> studentValue;

  const AssignSupplyDialogWidget(
      {super.key, required this.suppliesValue, required this.studentValue});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final supplyCtrl = ref.watch(getSupplyDataProvider(context));
    return supplyCtrl.get(data: (supplies) {
      if (supplies.isEmpty) {
        return Padding(
          padding: const EdgeInsets.only(top: 30.0),
          child: Center(
            child: Text(
              context.tr.supplies,
              style: textTheme(context).headlineMedium,
            ),
          ),
        );
      }
      return Column(
        children: [
          StudentDropDown(
            selectedStudent: studentValue as ValueNotifier<StudentModel?>,
          ),
          context.largeGap,
          ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              itemBuilder: (context, index) => AssignSupplyCard(
                    supply: supplies[index],
                    supplies: suppliesValue,
                  ),
              itemCount: supplies.length),
        ],
      );
    });
  }
}

class AssignSupplyCard extends HookWidget {
  final SupplyModel supply;
  final ValueNotifier<List<SupplyModel>> supplies;

  const AssignSupplyCard({
    super.key,
    required this.supply,
    required this.supplies,
  });

  @override
  Widget build(BuildContext context) {
    final value = useState(false);

    return Container(
      padding: const EdgeInsets.all(AppSpaces.mediumPadding),
      margin: const EdgeInsets.only(bottom: AppSpaces.smallPadding),
      width: double.infinity,
      decoration: BoxDecoration(
          border: Border.all(color: ColorManager.grey),
          color: ColorManager.white,
          borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
          boxShadow: ConstantsWidgets.boxShadowFromBottom),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                supply.name,
                style: context.subHeadLine,
                maxLines: 1,
              ),
              Checkbox(
                value: value.value,
                onChanged: (val) {
                  value.value = val!;

                  if (!supplies.value
                      .map((e) => e.id)
                      .toList()
                      .contains(supply.id!)) {
                    supplies.value.add(supply);
                  } else {
                    supplies.value.remove(supply);
                  }
                },
              ),
            ],
          ),
        ],
      ),
    );
  }
}
