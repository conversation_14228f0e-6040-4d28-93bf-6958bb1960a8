import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/supplies/model/supply_model.dart';
import 'package:connectify_app/src/screens/supplies/repo/teacher_supply_repo.dart';
import 'package:connectify_app/src/screens/supplies/view/admin_supplies/supplies_screen.dart';
import 'package:connectify_app/src/screens/teacher/controllers/teacher_controller.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import '../../../controller/teacher_supply_controller.dart';
import 'assign_supply_dialog_widget.dart';

class AssignSupplyDialog extends StatelessWidget {
  const AssignSupplyDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return AddRectangleWidget(
        title: context.tr.sendSupplyToStudent,
        onTap: () {
          showAssignSupplyDialog(
            context,
          );
        });
  }
}

Future<void> showAssignSupplyDialog(context) {
  return showDialog(
    context: context,
    builder: (context) {
      return HookConsumer(
        builder: (context, ref, child) {
          final formKey = useState(GlobalKey<FormState>());

          //!-----------------------------------------------------

          final teacherSupplyCtrl =
              ref.watch(teacherSupplyControllerChangeNotifierProvider(context));

          final supplies = useState<List<SupplyModel>>([]);
          final studentValue = useState<StudentModel?>(null);

          return AlertDialogWidget(
            header: context.tr.assignSupplyToStudent,
            isLoading: teacherSupplyCtrl.isLoading,
            isImage: false,
            child: Form(
              key: formKey.value,
              child: AssignSupplyDialogWidget(
                suppliesValue: supplies,
                studentValue: studentValue,
              ),
            ),
            onConfirm: () async {
              if (!formKey.value.currentState!.validate()) return;
              await teacherSupplyCtrl.addTeacherSupply(
                  supplies: supplies.value, student: studentValue.value!);
            },
          );
        },
      );
    },
  );
}
