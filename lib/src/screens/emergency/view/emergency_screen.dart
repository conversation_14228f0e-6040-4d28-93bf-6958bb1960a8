import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../home/<USER>/main_screen/widgets/main_app_bar.dart';

class EmergencyScreen extends HookConsumerWidget {
  const EmergencyScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final studentController = ref.watch(getStudentByIdProvider(context));

    final studentCtrl = ref.watch(studentChangeNotifierProvider(context));

    final formKey = useState(GlobalKey<FormState>());
    return Scaffold(
      appBar: MainAppBar(
        title: context.tr.emergency,
        isBackButton: true,
      ),
      body: studentController.get(
        data: (student) {
          return HookBuilder(builder: (context) {
            final controllers = {
              ApiStrings.homeAddress: useTextEditingController(
                text: student.homeAddress,
              ),
              ApiStrings.motherPhoneNumber: useTextEditingController(
                text: student.motherPhoneNumber,
              ),
              ApiStrings.parentPhoneNumber: useTextEditingController(
                text: student.motherPhoneNumber,
              ),
            };
            return Form(
              key: formKey.value,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    context.tr.emergencyInformation,
                    style: context.subHeadLine,
                  ),
                  context.mediumGap,
                  Text(
                    context.tr.addTheEmergency,
                    style: context.subHeadLine.copyWith(
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  context.fieldsGap,
                  BaseTextField(
                    title: context.tr.motherPhoneNumber,
                    controller: controllers[ApiStrings.motherPhoneNumber],
                    textInputType: TextInputType.phone,
                  ),
                  context.fieldsGap,
                  BaseTextField(
                    title: context.tr.parentPhoneNumber,
                    controller: controllers[ApiStrings.parentPhoneNumber],
                    textInputType: TextInputType.phone,
                  ),
                  context.fieldsGap,
                  BaseTextField(
                    title: context.tr.address,
                    maxLines: 3,
                    controller: controllers[ApiStrings.homeAddress],
                    textInputType: TextInputType.text,
                  ),
                  context.xxLargeGap,
                  Padding(
                    padding: const EdgeInsets.all(AppSpaces.largePadding),
                    child: Button(
                        loadingWidget: const LoadingWidget(),
                        isLoading: studentCtrl.isLoading,
                        label: context.tr.save,
                        onPressed: () async {
                          if (!formKey.value.currentState!.validate()) return;

                          await studentCtrl.editStudent(
                            controllers: controllers,
                            navigateWidget: const EmergencyScreen(),
                            id: UserModel.studentId(),
                          );
                        }),
                  ),
                ],
              ).scroll().paddingAll(AppSpaces.mediumPadding),
            );
          });
        },
      ),
    );
  }
}
