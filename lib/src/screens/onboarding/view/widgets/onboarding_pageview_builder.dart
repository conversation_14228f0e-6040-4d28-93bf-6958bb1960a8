import 'package:connectify_app/src/screens/onboarding/models/onboarding_model.dart';
import 'package:connectify_app/src/screens/onboarding/view/widgets/onboarding_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';

class OnBoardingPageViewBuilder extends HookWidget {
  final List<OnBoardingModel> onBoardingPages;

  const OnBoardingPageViewBuilder({super.key, required this.onBoardingPages});

  @override
  Widget build(BuildContext context) {
    final pageController = usePageController();

    return PageView.builder(
      controller: pageController,
      scrollDirection: Axis.horizontal,
      itemCount: onBoardingPages.length,
      itemBuilder: (context, index) {
        final onBoardingPage = onBoardingPages[index];

        return OnBoardingPage(
          onBoardingPage: onBoardingPage,
          isLast: index == onBoardingPages.length - 1,
          pageController: pageController,
        );
      },
    );
  }
}
