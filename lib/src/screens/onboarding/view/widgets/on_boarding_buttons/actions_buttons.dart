import 'package:connectify_app/src/screens/auth/view/sign_in_screen/sign_in_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class OnBoardingActionButtons extends StatelessWidget {
  final PageController pageController;

  const OnBoardingActionButtons({super.key, required this.pageController});

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        //? Skip Button
        TextButton(
            onPressed: () => context.toReplacement(const SignInScreen()),
            child: context.tr.skip.baseText(context, context.whiteSubTitle)),

        //? Next Button
        CircleAvatar(
          backgroundColor: Colors.white,
          maxRadius: 30,
          child: IconButton(
            onPressed: () {
              pageController.nextPage(
                  duration: const Duration(milliseconds: 500),
                  curve: Curves.easeIn);
            },
            icon: const Icon(
              Icons.arrow_forward_ios,
              color: ColorManager.primaryColor,
              size: 24,
            ),
          ),
        )
      ],
    );
  }
}
