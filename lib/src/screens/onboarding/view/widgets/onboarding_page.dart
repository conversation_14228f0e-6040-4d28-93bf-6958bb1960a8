import 'package:connectify_app/src/screens/onboarding/models/onboarding_model.dart';
import 'package:connectify_app/src/screens/onboarding/view/widgets/on_boarding_buttons/actions_buttons.dart';
import 'package:connectify_app/src/screens/onboarding/view/widgets/on_boarding_buttons/lets_start_button.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class OnBoardingPage extends StatelessWidget {
  final OnBoardingModel onBoardingPage;
  final bool isLast;
  final PageController pageController;

  const OnBoardingPage(
      {super.key,
      required this.onBoardingPage,
      this.isLast = false,
      required this.pageController});

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        //! Image
        Positioned.fill(
          child: BaseCachedImage(
            onBoardingPage.image?.url ?? '',
            fit: BoxFit.cover,
          ),
        ),

        context.mediumGap,

        //! Title & Subtitle
        Positioned.fill(
          child: Column(
            children: [
              const Spacer(
                flex: 3,
              ),

              //? Title
              onBoardingPage.title.baseText(context, context.whiteHeadLine,
                  textAlign: TextAlign.center),

              context.mediumGap,

              //? Subtitle
              onBoardingPage.subtitle.baseText(
                  context, context.whiteLabelMedium,
                  textAlign: TextAlign.center),

              context.xxLargeGap,

              //? OnBoarding Actions
              if (isLast)
                const LetsStartButton()
              else
                OnBoardingActionButtons(
                  pageController: pageController,
                ),

              context.largeGap,
            ],
          ).decorated(
            width: context.width * 0.9,
            padding:
                const EdgeInsets.symmetric(horizontal: AppSpaces.mediumPadding),
          ),
        )
      ],
    );
  }
}
