import 'package:connectify_app/src/screens/onboarding/controllers/onboarding_controller.dart';
import 'package:connectify_app/src/screens/onboarding/models/onboarding_model.dart';
import 'package:connectify_app/src/screens/onboarding/view/widgets/onboarding_pageview_builder.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class OnBoardingScreen extends ConsumerWidget {
  final List<OnBoardingModel> onBoardingPages;

  const OnBoardingScreen({super.key, required this.onBoardingPages});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final onBoardingCtrl = ref.watch(getOnBoardingFutureProvider(context));

    Widget onBoardingPageViewBuilder(List<OnBoardingModel> onBoardingPages) =>
        OnBoardingPageViewBuilder(
          onBoardingPages: onBoardingPages,
        );

    final getOnBoardingData = onBoardingCtrl.get(
      data: onBoardingPageViewBuilder,
    );

    // * Check if list already loaded from splash screen
    return Scaffold(
        body: onBoardingPages.isEmpty
            ? getOnBoardingData
            : onBoardingPageViewBuilder(onBoardingPages));
  }
}
