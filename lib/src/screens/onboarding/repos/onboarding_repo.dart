import 'package:connectify_app/src/screens/onboarding/models/onboarding_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:riverpod/riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

final onBoardingRepoProvider = Provider<OnBoardingRepo>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return OnBoardingRepo(networkApiService);
});

class OnBoardingRepo with BaseRepository {
  final BaseApiServices _networkApiService;

  OnBoardingRepo(this._networkApiService);

  //? Get OnBoarding Data
  Future<List<OnBoardingModel>> getOnBoardingPages() async {
    return baseFunction(
      () async {
        final response =
            await _networkApiService.getResponse(ApiEndpoints.config);

        final onBoardingPages =
            compute(responseToOnBoardingModelList, response);

        return onBoardingPages;
      },
    );
  }
}
