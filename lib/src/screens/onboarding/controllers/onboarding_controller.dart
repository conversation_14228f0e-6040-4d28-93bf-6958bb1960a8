import 'package:connectify_app/src/screens/onboarding/models/onboarding_model.dart';
import 'package:connectify_app/src/screens/onboarding/repos/onboarding_repo.dart';
import 'package:flutter/cupertino.dart';
import 'package:riverpod/riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

// * OnBoarding Controller ========================================
final onBoardingController =
    Provider.family<OnBoardingController, BuildContext>(
  (ref, context) {
    final onBoardingRepo = ref.watch(onBoardingRepoProvider);

    return OnBoardingController(context, onBoardingRepo: onBoardingRepo);
  },
);

// * Get OnBoarding Data ========================================
final getOnBoardingFutureProvider =
    FutureProvider.family<List<OnBoardingModel>, BuildContext>(
  (ref, context) async {
    final onBoardingCtrl = ref.watch(onBoardingController(context));

    return await onBoardingCtrl.getOnBoardingData();
  },
);

class OnBoardingController extends BaseVM {
  final BuildContext context;
  final OnBoardingRepo onBoardingRepo;

  OnBoardingController(
    this.context, {
    required this.onBoardingRepo,
  });

  Future<List<OnBoardingModel>> getOnBoardingData() async {
    return await baseFunction(
      context,
      () async {
        final onBoardingPages = await onBoardingRepo.getOnBoardingPages();

        return onBoardingPages;
      },
    );
  }
}
