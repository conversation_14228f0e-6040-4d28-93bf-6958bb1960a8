import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/shared_models/base_media_model.dart';
import 'package:equatable/equatable.dart';

List<OnBoardingModel> responseToOnBoardingModelList(response) {
  final data = (response[ApiStrings.data][ApiStrings.attributes]
          [ApiStrings.onBoarding] as List?) ??
      [];

  final onBoardingPages = data.map((e) => OnBoardingModel.fromJson(e)).toList();

  return onBoardingPages;
}

class OnBoardingModel extends Equatable {
  final String title;
  final String subtitle;
  final BaseMediaModel? image;

  const OnBoardingModel({this.title = '', this.subtitle = '', this.image});

  //? From Json
  factory OnBoardingModel.fromJson(Map<String, dynamic> json) {
    final image = json[ApiStrings.image] != null
        ? BaseMediaModel.fromJson(
            json[ApiStrings.image][ApiStrings.data][ApiStrings.attributes])
        : null;

    return OnBoardingModel(
        title: json[ApiStrings.title] ?? '',
        subtitle: json[ApiStrings.subtitle] ?? '',
        image: image);
  }

  @override
  List<Object?> get props => [title, subtitle, image];
}
