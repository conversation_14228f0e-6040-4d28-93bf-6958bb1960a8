import 'package:connectify_app/src/screens/messages/model/messages_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../shared/widgets/base_container.dart';
import '../../shared/widgets/shared_widgets.dart';

class ChatScreen extends HookWidget {
  final List<MessageModel> messages;

  const ChatScreen({Key? key, required this.messages}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final scrollController = useScrollController();

    useEffect(() {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        scrollController.animateTo(
          scrollController.position.maxScrollExtent,
          duration: const Duration(milliseconds: 200),
          curve: Curves.easeInOut,
        );
      });
      return () {};
    }, []);

    final sortListWithFirstDate = messages
        .where((element) => element.createdAt != null)
        .toList()
      ..sort((a, b) => a.createdAt!.compareTo(b.createdAt ?? ''));

    return Scaffold(
      body: ListView.separated(
        controller: scrollController,
        padding: EdgeInsets.only(
          top: AppSpaces.mediumPadding,
          right: AppSpaces.mediumPadding,
          left: AppSpaces.mediumPadding,
          bottom: 70.h,
        ),
        itemCount: messages.length,
        itemBuilder: (context, index) {
          final isFromParent = sortListWithFirstDate[index].type == 'parent';

          final isToTeacher =
              isFromParent && sortListWithFirstDate[index].teacher != null;

          final isToAdmin =
              isFromParent && sortListWithFirstDate[index].teacher == null;

          return Padding(
            padding: EdgeInsets.only(
              right: isFromParent ? 0 : AppSpaces.xlLargePadding,
              left: isFromParent ? AppSpaces.xlLargePadding : 0,
            ),
            child: BaseContainer(
              borderColor: Colors.transparent,
              color: isFromParent
                  ? ColorManager.senderColor
                  : ColorManager.messageColor,
              onTap: () {},
              boxShadow: ConstantsWidgets.boxShadow,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  if (isToTeacher)
                    Text(
                      context.tr.toTeacher,
                      textAlign: TextAlign.end,
                      style: context.boldTitle.copyWith(
                        color: ColorManager.primaryColor,
                      ),
                    ),
                  if (isToAdmin)
                    Text(
                      context.tr.toAdmin,
                      style: context.boldTitle.copyWith(
                        color: ColorManager.primaryColor,
                      ),
                    ),
                  Text(
                    sortListWithFirstDate[index]
                        .createdAt
                        .formatDateTimeToString,
                    style: context.labelMedium,
                  ),
                  context.mediumGap,
                  Text(
                    sortListWithFirstDate[index].title ?? '',
                    style: context.boldTitle,
                  ),
                  context.mediumGap,
                  Text(
                    sortListWithFirstDate[index].description ?? '',
                    style: context.title,
                  ),
                ],
              ),
            ),
          );
        },
        separatorBuilder: (context, index) => context.largeGap,
      ),
    );
  }
}
