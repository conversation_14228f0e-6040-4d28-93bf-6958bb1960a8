import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:equatable/equatable.dart';

List<ToiletModel> responseToToiletModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final toiletData = data.map((e) => ToiletModel.fromJson(e)).toList();

  return toiletData;
}

enum ToiletType { urine, stool }

enum ToiletWay { diaper, clothes, toilet }

class ToiletModel extends Equatable {
  final int? id;
  final ToiletType? toiletType;
  final ToiletWay? toiletWay;
  final DateTime? createdAt;


  const ToiletModel({
    this.id,
    this.toiletType,
    this.toiletWay,
    this.createdAt,
  });

  factory ToiletModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];
    return ToiletModel(
      id: json[ApiStrings.id],
      toiletType: getToiletTypeFromApi(attributes[ApiStrings.toiletType]),
      toiletWay: getToiletWayFromApi(attributes[ApiStrings.toiletWay]),
      createdAt: DateTime.parse(attributes[ApiStrings.createdAt]).toLocal(),
    );
  }

  static ToiletType getToiletTypeFromApi(String value) {
    switch (value) {
      case "Urine":
        return ToiletType.urine;
      case "Stool":
        return ToiletType.stool;

      default:
        return ToiletType.urine;
    }
  }

  static ToiletWay getToiletWayFromApi(String value) {
    switch (value) {
      case "Diaper":
        return ToiletWay.diaper;
      case "Clothes":
        return ToiletWay.clothes;
      case "Toilet":
        return ToiletWay.toilet;

      default:
        return ToiletWay.diaper;
    }
  }

  String getToiletType() {
    switch (toiletType) {
      case ToiletType.urine:
        return "Urine";
      case ToiletType.stool:
        return "Stool";

      default:
        return 'Urine';
    }
  }

  static ToiletType getToiletTypeValue({required int value}) {
    switch (value) {
      case 0:
        return ToiletType.urine;
      case 1:
        return ToiletType.stool;

      default:
        return ToiletType.urine;
    }
  }

  String getToiletWay() {
    switch (toiletWay) {
      case ToiletWay.diaper:
        return "Diaper";
      case ToiletWay.clothes:
        return "Clothes";
      case ToiletWay.toilet:
        return "Toilet";

      default:
        return 'Diaper';
    }
  }

  static ToiletWay getToiletWayValue({required int value}) {
    switch (value) {
      case 0:
        return ToiletWay.diaper;
      case 1:
        return ToiletWay.clothes;
      case 2:
        return ToiletWay.clothes;

      default:
        return ToiletWay.toilet;
    }
  }

  @override
  List<Object?> get props => [id, toiletType, toiletWay];
}
