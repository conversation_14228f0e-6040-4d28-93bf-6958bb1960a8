import 'package:connectify_app/src/screens/toilet/model/toilet_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

// * =========================================================

final toiletRepoProvider = Provider((ref) {
  final networkApiServices = ref.watch(networkServiceProvider);

  return ToiletRepo(networkApiServices);
});

//? ========================================================

class ToiletRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  ToiletRepo(this._networkApiServices);

//? get Toilet Data ========================================================
  Future<List<ToiletModel>> getToiletData() async {
    return await baseFunction(() async {
      final response =
          await _networkApiServices.getResponse(ApiEndpoints.toilets);

      final toiletData = await compute(responseToToiletModelList, response);

      return toiletData;
    });
  }

  Future<List<ToiletModel>> getToiletDataByDay({required DateTime date}) async {
    return await baseFunction(() async {
      final response = await _networkApiServices.getResponse(
          '${ApiEndpoints.toilets}&${ApiEndpoints.filterByDate(date)}');

      final toiletData = await compute(responseToToiletModelList, response);

      return toiletData;
    });
  }
}
