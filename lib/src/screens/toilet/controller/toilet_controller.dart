import 'package:connectify_app/src/screens/toilet/repo/toilet_repo.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../model/toilet_model.dart';

final toiletControllerProvider =
    Provider.family<ToiletController, BuildContext>((ref, context) {
  final toiletRepo = ref.watch(toiletRepoProvider);

  return ToiletController(toiletRepo: toiletRepo, context: context);
});
final toiletChangeNotifierControllerProvider =
    ChangeNotifierProvider.family<ToiletController, BuildContext>(
        (ref, context) {
  final toiletRepo = ref.watch(toiletRepoProvider);

  return ToiletController(toiletRepo: toiletRepo, context: context);
});

final getToiletByDayData =
    FutureProvider.family<List<ToiletModel>, (BuildContext, DateTime)>(
        (ref, params) {
  final toiletRepo = ref.watch(toiletRepoProvider);
  final context = params.$1;

  final toiletController = ToiletController(
    toiletRepo: toiletRepo,
    context: context,
  );

  final date = params.$2;

  return toiletController.getToiletDataByDay(date: date);
});

final getAllToiletData =
    FutureProvider.family<List<ToiletModel>, BuildContext>((ref, context) {
  final toiletRepo = ref.watch(toiletRepoProvider);

  final toiletController = ToiletController(
    toiletRepo: toiletRepo,
    context: context,
  );

  return toiletController.getToiletData();
});

class ToiletController extends BaseVM {
  final ToiletRepo toiletRepo;
  final BuildContext context;

  ToiletController({required this.toiletRepo, required this.context});

  Future<List<ToiletModel>> getToiletData() async {
    return await baseFunction(context, () async {
      final toiletData = await toiletRepo.getToiletData();

      return toiletData;
    });
  }

  // get toilet by day
  Future<List<ToiletModel>> getToiletDataByDay({required DateTime date}) async {
    return await baseFunction(context, () async {
      final toiletData = await toiletRepo.getToiletDataByDay(
        date: date,
      );

      return toiletData;
    });
  }
}
