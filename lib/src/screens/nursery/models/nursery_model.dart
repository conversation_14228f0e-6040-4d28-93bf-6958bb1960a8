import 'dart:developer';

import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/nursery/models/payment_methods_model.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../shared/data/remote/api_strings.dart';
import '../../../shared/shared_models/base_media_model.dart';

class NurseryModel extends UserModel {
  final int maxStudents;
  final UserModel? admin;
  final bool canContactTeacher;
  final bool showNurseryLogoInParentApp;
  final DateTime? endDate;
  final PaymentMethodsModel? paymentMethods;

  const NurseryModel({
    super.id,
    super.name,
    super.image,
    this.admin,
    this.maxStudents = AppConsts.maxStudents,
    this.canContactTeacher = true,
    this.showNurseryLogoInParentApp = false,
    this.endDate,
    this.paymentMethods,
  });

  factory NurseryModel.fromAttributesJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];

    final logo = attributes != null &&
            attributes.containsKey(ApiStrings.logo) &&
            attributes[ApiStrings.logo][ApiStrings.data] != null
        ? BaseMediaModel.fromJson(
            attributes[ApiStrings.logo][ApiStrings.data][ApiStrings.attributes])
        : null;

    final paymentMethods = attributes != null && attributes[ApiStrings.paymentMethods] != null
        ? PaymentMethodsModel.fromJson(attributes[ApiStrings.paymentMethods])
        : null;

    return NurseryModel(
      id: json[ApiStrings.id],
      name: attributes != null ? (attributes[ApiStrings.name] ?? '') : '',
      maxStudents: attributes[ApiStrings.maxStudents] != null
          ? (attributes[ApiStrings.maxStudents])
          : AppConsts.maxStudents,
      image: logo,
      canContactTeacher: attributes[ApiStrings.canContactTeacher] ?? true,
      showNurseryLogoInParentApp:
          attributes[ApiStrings.showNurseryLogoInParentApp] ?? false,
      admin: attributes[ApiStrings.admin] != null
          ? UserModel.fromJson(attributes[ApiStrings.admin])
          : null,
      endDate: attributes[ApiStrings.endDate] != null
          ? DateTime.parse(attributes[ApiStrings.endDate])
          : null,
      paymentMethods: paymentMethods,
    );
  }

  factory NurseryModel.fromJson(Map<String, dynamic> attributes) {
    final logo = attributes[ApiStrings.logo] != null
        ? BaseMediaModel.fromJson(attributes[ApiStrings.logo])
        : null;

    final paymentMethods = attributes[ApiStrings.paymentMethods] != null
        ? PaymentMethodsModel.fromJson(attributes[ApiStrings.paymentMethods])
        : null;

    return NurseryModel(
      id: attributes[ApiStrings.id],
      maxStudents: attributes[ApiStrings.maxStudents] ?? AppConsts.maxStudents,
      admin: attributes[ApiStrings.admin] != null
          ? UserModel.fromJson(attributes[ApiStrings.admin])
          : null,
      name: attributes[ApiStrings.name] ?? '',
      canContactTeacher: attributes[ApiStrings.canContactTeacher] ?? true,
      showNurseryLogoInParentApp:
          attributes[ApiStrings.showNurseryLogoInParentApp] ?? false,
      image: logo,
      endDate: attributes[ApiStrings.endDate] != null
          ? DateTime.parse(attributes[ApiStrings.endDate])
          : null,
      paymentMethods: paymentMethods,
    );
  }

  //? Get Nursery ID
  static NurseryModel? currentNursery() {
    final nursery = GetStorageService.getLocalData(key: LocalKeys.nursery);

    if (nursery == null) return null;

    if (nursery.containsKey('attributes')) {
      return NurseryModel.fromAttributesJson(nursery);
    }

    if (nursery.containsKey('data')) {
      return NurseryModel.fromAttributesJson(nursery['data']);
    }

    // final nurseryModel =
    //     nursery['data'] is List ? nursery['data'][0] : nursery['data'];

    return NurseryModel.fromJson(nursery);
  }

  //? Get Nursery ID
  static currentNurseryId() {
    final nursery = GetStorageService.getLocalData(key: LocalKeys.nursery);

    log('asfasfasf $nursery');

    if (nursery == null) return null;

    int? nurseryId;

    // final firstNursery =
    //     nursery['data'] is List ? nursery['data'][0] : nursery['data'];

    log('asfasfasf $nursery');

    nurseryId = nursery['id'];

    // log('asfasffffffasf $nurseryId');

    return nurseryId;
  }

  // copyWith method
  @override
  NurseryModel copyWith({
    int? id,
    String? name,
    String? description,
    BaseMediaModel? image,
    String? phone,
    List<ClassModel>? classes,
    UserTypeEnum? userType,
    String? email,
    String? password,
    String? fcmToken,
    // NurseryModel specific parameters
    UserModel? admin,
    int? maxStudents,
    bool? canContactTeacher,
    bool? showNurseryLogoInParentApp,
    DateTime? endDate,
    PaymentMethodsModel? paymentMethods,
  }) {
    return NurseryModel(
      id: id ?? this.id,
      name: name ?? this.name,
      image: image ?? this.image,
      admin: admin ?? this.admin,
      maxStudents: maxStudents ?? this.maxStudents,
      canContactTeacher: canContactTeacher ?? this.canContactTeacher,
      showNurseryLogoInParentApp: showNurseryLogoInParentApp ?? this.showNurseryLogoInParentApp,
      endDate: endDate ?? this.endDate,
      paymentMethods: paymentMethods ?? this.paymentMethods,
    );
  }

  // to json
  Map<String, dynamic> toDataJson() {
    return {
      ApiStrings.id: id,
      ApiStrings.name: name,
      ApiStrings.logo: image?.toJson(),
      ApiStrings.maxStudents: maxStudents,
      ApiStrings.canContactTeacher: canContactTeacher,
      ApiStrings.showNurseryLogoInParentApp: showNurseryLogoInParentApp,
      if (paymentMethods != null) ApiStrings.paymentMethods: paymentMethods!.toJson(),
    };
  }
}
