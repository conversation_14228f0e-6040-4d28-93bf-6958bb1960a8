import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:equatable/equatable.dart';

class PaymentMethodsModel extends Equatable {
  final String? instapay;
  final String? vodafoneCash;
  final String? etisalatCash;
  final String? weCash;
  final String? orangeCash;

  const PaymentMethodsModel({
    this.instapay,
    this.vodafoneCash,
    this.etisalatCash,
    this.weCash,
    this.orangeCash,
  });

  factory PaymentMethodsModel.fromJson(Map<String, dynamic> json) {
    return PaymentMethodsModel(
      instapay: json[ApiStrings.instapay],
      vodafoneCash: json[ApiStrings.vodafoneCash],
      etisalatCash: json[ApiStrings.etisalatCash],
      weCash: json[ApiStrings.weCash],
      orangeCash: json[ApiStrings.orangeCash],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (instapay != null) ApiStrings.instapay: instapay,
      if (vodafoneCash != null) ApiStrings.vodafoneCash: vodafoneCash,
      if (etisalatCash != null) ApiStrings.etisalatCash: etisalatCash,
      if (weCash != null) ApiStrings.weCash: weCash,
      if (orangeCash != null) ApiStrings.orangeCash: orangeCash,
    };
  }

  PaymentMethodsModel copyWith({
    String? instapay,
    String? vodafoneCash,
    String? etisalatCash,
    String? weCash,
    String? orangeCash,
  }) {
    return PaymentMethodsModel(
      instapay: instapay ?? this.instapay,
      vodafoneCash: vodafoneCash ?? this.vodafoneCash,
      etisalatCash: etisalatCash ?? this.etisalatCash,
      weCash: weCash ?? this.weCash,
      orangeCash: orangeCash ?? this.orangeCash,
    );
  }

  /// Check if InstaPay is a link (contains http/https)
  bool get isInstapayLink => instapay != null && 
      (instapay!.contains('http') || instapay!.contains('https'));

  /// Get available payment methods (non-null values)
  List<String> get availablePaymentMethods {
    final methods = <String>[];
    if (instapay != null && instapay!.isNotEmpty) methods.add('InstaPay');
    if (vodafoneCash != null && vodafoneCash!.isNotEmpty) methods.add('Vodafone Cash');
    if (etisalatCash != null && etisalatCash!.isNotEmpty) methods.add('Etisalat Cash');
    if (weCash != null && weCash!.isNotEmpty) methods.add('WE Cash');
    if (orangeCash != null && orangeCash!.isNotEmpty) methods.add('Orange Cash');
    return methods;
  }

  /// Get payment method value by name
  String? getPaymentMethodValue(String methodName) {
    switch (methodName.toLowerCase()) {
      case 'instapay':
        return instapay;
      case 'vodafone cash':
        return vodafoneCash;
      case 'etisalat cash':
        return etisalatCash;
      case 'we cash':
        return weCash;
      case 'orange cash':
        return orangeCash;
      default:
        return null;
    }
  }

  @override
  List<Object?> get props => [instapay, vodafoneCash, etisalatCash, weCash, orangeCash];
}
