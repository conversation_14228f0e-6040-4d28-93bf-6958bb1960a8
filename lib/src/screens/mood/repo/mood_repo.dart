import 'package:connectify_app/src/screens/mood/model/mood_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

// * =========================================================

final moodRepoProvider = Provider((ref) {
  final networkApiServices = ref.watch(networkServiceProvider);

  return MoodRepo(networkApiServices);
});

//? ========================================================

class MoodRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  MoodRepo(this._networkApiServices);

//? get Mood Data ========================================================
  Future<List<MoodModel>> getMoodData() async {
    return await baseFunction(() async {
      final response = _networkApiServices.getResponse(ApiEndpoints.mood);

      final moodData = await compute(responseToMoodModelList, response);

      return moodData;
    });
  }

  Future<List<MoodModel>> getMoodDataByDay({required DateTime date}) async {
    return await baseFunction(() async {
      final response = await _networkApiServices.getResponse(
          '${ApiEndpoints.mood}&${ApiEndpoints.filterByDate(date)}');

      final moodData = await compute(responseToMoodModelList, response);

      return moodData;
    });
  }
}
