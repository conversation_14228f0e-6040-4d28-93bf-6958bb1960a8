import 'package:connectify_app/src/screens/mood/repo/mood_repo.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../model/mood_model.dart';

final moodControllerProvider =
    Provider.family<MoodController, BuildContext>((ref, context) {
  final moodRepo = ref.watch(moodRepoProvider);

  return MoodController(moodRepo: moodRepo, context: context);
});

final moodChangeNotifierControllerProvider =
    ChangeNotifierProvider.family<MoodController, BuildContext>((ref, context) {
  final moodRepo = ref.watch(moodRepoProvider);

  return MoodController(moodRepo: moodRepo, context: context);
});

final getAllMoodData =
    FutureProvider.family<List<MoodModel>, BuildContext>((ref, context) {
  final moodRepo = ref.watch(moodRepoProvider);

  final moodController = MoodController(moodRepo: moodRepo, context: context);

  return moodController.getMoodData();
});

final getMoodByDayData =
    FutureProvider.family<List<MoodModel>, (BuildContext, DateTime)>(
        (ref, params) {
  final moodRepo = ref.watch(moodRepoProvider);
  final context = params.$1;

  final moodController = MoodController(moodRepo: moodRepo, context: context);

  final date = params.$2;

  return moodController.getMoodDataByDay(date: date);
});

class MoodController extends BaseVM {
  final MoodRepo moodRepo;
  final BuildContext context;

  MoodController({required this.moodRepo, required this.context});

  Future<List<MoodModel>> getMoodData() async {
    return await baseFunction(context, () async {
      final moodData = await moodRepo.getMoodData();

      return moodData;
    });
  }

  Future<List<MoodModel>> getMoodDataByDay({required DateTime date}) async {
    return await baseFunction(context, () async {
      final moodData = await moodRepo.getMoodDataByDay(date: date);

      return moodData;
    });
  }
}
