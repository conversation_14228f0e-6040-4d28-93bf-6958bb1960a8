import 'package:connectify_app/src/screens/messages/view/widgets/messages__card_widget.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../model/messages_model.dart';

class HomeMessagesList extends ConsumerWidget {
  final List<MessageModel> messagesList;

  const HomeMessagesList({
    super.key,
    required this.messagesList,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ListView.separated(
      padding: const EdgeInsets.only(
        right: AppSpaces.mediumPadding,
        left: AppSpaces.mediumPadding,
        bottom: AppSpaces.largePadding,
      ),
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: messagesList.length,
      itemBuilder: (context, index) {
        return MessagesCardWidget(
          message: messagesList[index],
        );
      },
      separatorBuilder: (context, index) => context.mediumGap,
    );
  }
}
