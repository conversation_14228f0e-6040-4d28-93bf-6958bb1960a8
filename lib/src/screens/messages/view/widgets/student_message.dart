import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/screens/messages/view/messages_screen.dart';
import 'package:connectify_app/src/screens/messages/view/widgets/add_message/add_message.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/widgets/base_container.dart';
import '../../../../shared/widgets/shared_widgets.dart';
import '../../../student/models/student_model.dart';

class StudentMessages extends ConsumerWidget {
  final StudentModel student;

  const StudentMessages({super.key, required this.student});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final List messagesList = [
      BaseContainer(
        boxShadow: ConstantsWidgets.boxShadow,
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                    child: Text(
                  'Need more supplies',
                  style: context.subTitle.copyWith(fontWeight: FontWeight.bold),
                )),
                Text(
                  '12/5/2023',
                  style: context.labelMedium,
                ),
                context.smallGap,
                Text(
                  '12:30',
                  style: context.labelMedium,
                ),
              ],
            ),
            context.mediumGap,
            Text(
              'Lorem Ipsum is simply dummy text of the printing and typesetting industry.Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
              style: context.subTitle,
            )
          ],
        ),
      ).paddingOnly(bottom: 20, top: 10),
      BaseContainer(
        boxShadow: ConstantsWidgets.boxShadow,
        child: Column(
          children: [
            Row(
              children: [
                Expanded(
                    child: Text(
                  'Need more supplies',
                  style: context.subTitle.copyWith(fontWeight: FontWeight.bold),
                )),
                Text(
                  '12/5/2023',
                  style: context.labelMedium,
                ),
                context.smallGap,
                Text(
                  '12:30',
                  style: context.labelMedium,
                ),
              ],
            ),
            context.mediumGap,
            Text(
              'Lorem Ipsum is simply dummy text of the printing and typesetting industry.Lorem Ipsum is simply dummy text of the printing and typesetting industry.',
              style: context.subTitle,
            )
          ],
        ),
      ),
    ];

    return Scaffold(
      appBar: MainAppBar(
        title: student.name,
        isBackButton: true,
      ),
      body: Column(
        children: [
          BaseContainer(
            padding: AppSpaces.smallPadding,
            boxShadow: ConstantsWidgets.boxShadow,
            child: ClipRRect(
              borderRadius:
                  BorderRadius.circular(AppRadius.baseContainerRadius),
              child: Row(
                children: [
                  Container(
                    height: 30.h,
                    width: 35.w,
                    decoration: const BoxDecoration(
                      shape: BoxShape.circle,
                    ),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(30),
                      child: BaseCachedImage(
                        student.image?.url ?? '',
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  context.mediumGap,
                  Text(
                    student.name ?? '',
                    style: context.blueHint.copyWith(fontSize: 16),
                  ),
                ],
              ),
            ),
          ),
          context.mediumGap,
          ...List.generate(messagesList.length, (index) => messagesList[index]),
        ],
      ).paddingAll(AppSpaces.mediumPadding),
      bottomNavigationBar: Button(
          label: context.tr.sendANewMessageTo(student.name),
          onPressed: () {
            showAddMessageDialog(
                student: false,
                context,
                navigateWidget: const MessagesScreen());
          }).paddingAll(AppSpaces.mediumPadding),
    );
  }
}
