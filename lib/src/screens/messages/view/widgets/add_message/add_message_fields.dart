import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/drop_downs/student_drop_down.dart';
import 'package:flutter/cupertino.dart';
import 'package:xr_helper/xr_helper.dart';

class AddMessageFields extends StatelessWidget {
  final Map<String, TextEditingController> controllers;
  final ValueNotifier<StudentModel?> selectedStudent;
  final int? studentId;
  final bool student;

  const AddMessageFields({
    super.key,
    required this.controllers,
    required this.selectedStudent,
    required this.studentId,  this.student = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        if(student)
        StudentDropDown(selectedStudent: selectedStudent, studentId: studentId),

        context.largeGap,

        //! Name
        BaseTextField(
          controller: controllers[ApiStrings.name],
          title: context.tr.title,
          textInputType: TextInputType.text,
        ),

        context.largeGap,

        //! Description
        BaseTextField(
          controller: controllers[ApiStrings.description],
          title: context.tr.message,
          textInputType: TextInputType.text,
          maxLines: 6,
          isRequired: false,
        ),
      ],
    );
  }
}
