import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/messages/view/widgets/add_message/add_message_fields.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/services/media/controller/media_controller.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class AddMessageDialog extends HookConsumerWidget {
  final Widget navigateWidget;

  const AddMessageDialog({super.key, required this.navigateWidget});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaController = ref.watch(mediaPickerControllerProvider);

    return AddRectangleWidget(onTap: () {
      showAddMessageDialog(
        context,
        navigateWidget: navigateWidget,
      ).then((value) {
        mediaController.clearFiles();
      });
    });
  }
}

Future<void> showAddMessageDialog(BuildContext context,
    {required Widget navigateWidget,
    ClassModel? classModel,
    student = true}) async {
  showDialog(
      context: context,
      builder: (context) {
        return HookConsumer(
          builder: (context, ref, child) {
            final controllers = {
              ApiStrings.name: useTextEditingController(text: classModel?.name),
              ApiStrings.description:
                  useTextEditingController(text: classModel?.description),
            };
            final formKey = useState(GlobalKey<FormState>());

            final selectedStudent = useState<StudentModel?>(null);

            return AlertDialogWidget(
                isImage: false,
                header: context.tr.sendANewMessage,
                isLoading: false,
                child: Form(
                  key: formKey.value,
                  child: AddMessageFields(
                    student: student,
                    selectedStudent: selectedStudent,
                    studentId: 2,
                    controllers: controllers,
                  ),
                ),
                onConfirm: () async {
                  // await addAndEditClass();
                });
          },
        );
      });
}
