import 'package:connectify_app/src/screens/home/<USER>/bottom_nav_controller.dart';
import 'package:connectify_app/src/screens/messages/model/messages_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../generated/assets.dart';
import '../../../../shared/widgets/base_container.dart';
import '../../../../shared/widgets/shared_widgets.dart';

class MessagesCardWidget extends ConsumerWidget {
  final MessageModel message;

  const MessagesCardWidget({super.key, required this.message});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final bottomNavBarCtrl = ref.read(bottomNavController);
    return BaseContainer(
      onTap: () {
        bottomNavBarCtrl.changeIndex(3);
      },
      padding: AppSpaces.smallPadding,
      boxShadow: ConstantsWidgets.boxShadow,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
        child: Row(
          children: [
            Container(
              height: 30.h,
              width: 35.w,
              decoration: const BoxDecoration(
                shape: BoxShape.circle,
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(30),
                child: BaseCachedImage(
                  message.admin?.image?.url ?? '',
                  errorWidget: Image.asset(Assets.imagesLogo),
                  fit: BoxFit.cover,
                ),
              ),
            ),
            context.mediumGap,
            Text(
              message.title ?? '',
              style: context.blueHint.copyWith(fontSize: 16),
            ),
            const Spacer(),
            const Icon(
              Icons.arrow_forward_ios_outlined,
              size: 15,
            )
          ],
        ),
      ),
    );
  }
}
