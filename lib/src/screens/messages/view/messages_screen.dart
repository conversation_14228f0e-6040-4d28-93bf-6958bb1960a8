import 'package:connectify_app/src/screens/messages/controller/messages_controller.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../chat/chat_screen.dart';
import '../../home/<USER>/main_screen/widgets/floating_button.dart';
import '../../parent_activity/view/parent_activity_screen.dart';

class MessagesScreen extends ConsumerWidget {
  const MessagesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final messageController = ref.watch(getAllMessageData(context));

    return Scaffold(
        body: Stack(
      alignment: Alignment.bottomCenter,
      children: [
        messageController.get(
          data: (messages) {
            return ChatScreen(messages: messages);
          },
        ),
        Row(
          children: [
            Expanded(
              child: Button(
                  label: context.tr.sendANewMessageToAdmin,
                  onPressed: () {
                    showSendMessageDialog(context,
                        isAdmin: true,
                        navigateWidget: const ParentActivitiesScreen());
                  }),
            ),
            if (NurseryModelHelper.currentNursery()?.canContactTeacher ==
                true) ...[
              context.mediumGap,
              Expanded(
                child: Button(
                    label: context.tr.sendANewMessageToTeacher,
                    onPressed: () {
                      showSendMessageDialog(context,
                          navigateWidget: const ParentActivitiesScreen(),
                          isAdmin: false);
                    }),
              ),
            ]
          ],
        ).paddingAll(AppSpaces.mediumPadding),
      ],
    ));
  }
}
