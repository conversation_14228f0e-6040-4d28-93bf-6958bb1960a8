import 'dart:developer';

import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/class/repos/class_repo.dart';
import 'package:connectify_app/src/screens/home/<USER>/bottom_nav_controller.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/screens/messages/repo/messages_repo.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../model/messages_model.dart';

final messageControllerProvider =
    Provider.family<MessageController, BuildContext>((ref, context) {
  final messageRepo = ref.watch(messageRepoProvider);
  final classRepo = ref.watch(classRepoProvider);

  return MessageController(
      messageRepo: messageRepo, context: context, classRepo: classRepo);
});
final messageChangeNotifierControllerProvider =
    ChangeNotifierProvider.family<MessageController, BuildContext>(
        (ref, context) {
  final messageRepo = ref.watch(messageRepoProvider);

  final classRepo = ref.watch(classRepoProvider);

  return MessageController(
      messageRepo: messageRepo, context: context, classRepo: classRepo);
});

final getHomeMessageData =
    FutureProvider.family<List<MessageModel>, BuildContext>((ref, context) {
  final messageRepo = ref.watch(messageRepoProvider);
  final classRepo = ref.watch(classRepoProvider);

  final messageController = MessageController(
      messageRepo: messageRepo, context: context, classRepo: classRepo);

  return messageController.getMessageData(withPopulate: false);
});

final getAllMessageData =
    FutureProvider.family<List<MessageModel>, BuildContext>((ref, context) {
  final messageRepo = ref.watch(messageRepoProvider);
  final classRepo = ref.watch(classRepoProvider);

  final messageController = MessageController(
      messageRepo: messageRepo, context: context, classRepo: classRepo);

  return messageController.getMessageData();
});

class MessageController extends BaseVM {
  final MessageRepo messageRepo;
  final ClassRepo classRepo;
  final BuildContext context;

  MessageController(
      {required this.messageRepo,
      required this.context,
      required this.classRepo});

  Future<List<MessageModel>> getMessageData({
    bool withPopulate = true,
  }) async {
    return await baseFunction(context, () async {
      final messageData = await messageRepo.getMessageData(
        withPopulate: withPopulate,
      );

      return messageData;
    });
  }

  //? Add Message ========================================================
  Future<void> sendMessage(WidgetRef ref,
      {required String title,
      required String description,
      required bool isAdmin}) async {
    // log('ujiuju9iuju9hu8i: ${studentClass.nursery?.admin?.id}');

    return await baseFunction(context, () async {
      final studentClass = await getStudentClass();

      final bottomNavIndex = ref.read(bottomNavController);

      log('asasfsafas ${studentClass.teachers?.firstOrNull?.id}');

      final message = MessageModel(
          title: title,
          description: description,
          admin: isAdmin
              ? UserModel(
                  id: studentClass.nursery?.admin?.id,
                )
              : null,
          teacher: isAdmin
              ? null
              : TeacherModel(
                  id: studentClass.teachers?.firstOrNull?.id,
                ));

      await messageRepo.addMessage(message: message);

      NotificationService.sendNotification(
        title: "New Message",
        body:
            "You have a new message from ${const UserModel().currentUser.name}.\n(Body : $description)",
        userTokenOrTopic: isAdmin
            ? NurseryModelHelper.adminTopic()
            : NurseryModelHelper.teacherByClassTopic(
                studentClass.id,
              ),
        isTopic: true,
      );

      if (!context.mounted) return;
      context.back();
      context.showBarMessage(context.tr.addedSuccessfully);
      bottomNavIndex.changeIndex(3);
      context.to(const MainScreen());
    });
  }

  //get all classes then filter and get first by UserModel.studentId
  Future<ClassModel> getStudentClass() async {
    final classes = await classRepo.getClasses();
    final studentClass = classes.firstOrNull;

    // classes.firstWhereOrNull(
    //     (element) => element.students!.any((e) => e.id == studentId));
    return studentClass ?? const ClassModel();
  }
}
