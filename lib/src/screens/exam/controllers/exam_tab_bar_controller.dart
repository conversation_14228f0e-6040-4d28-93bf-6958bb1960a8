import 'package:flutter_riverpod/flutter_riverpod.dart';

// * Bottom Nav Bar Controller ========================================
final examTabBarController = Provider<ExamTabBarController>(
  (ref) {
    return ExamTabBarController();
  },
);

// * Bottom Nav Bar State Notifier ========================================
final examTabBarControllerProvider =
    StateNotifierProvider<ExamTabBarController, int>(
  (ref) => ref.watch(examTabBarController),
);

class ExamTabBarController extends StateNotifier<int> {
  ExamTabBarController() : super(0);

  void changeIndex(int index) {
    state = index;
  }
}
