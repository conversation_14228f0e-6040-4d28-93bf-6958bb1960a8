import 'package:connectify_app/src/screens/exam/models/exam_model.dart';
import 'package:connectify_app/src/screens/exam/repos/exams_repo.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../view/exam_screen.dart';

// *  Exams Controller Provider ============================================
final examsControllerProvider =
    Provider.family<ExamsController, BuildContext>((ref, context) {
  final examsRepo = ref.watch(examRepoProvider);

  return ExamsController(examsRepo: examsRepo, context);
});

// *  Exams Controller Change Notifier Provider ============================================
final examsControllerChangeNotifierProvider =
    ChangeNotifierProvider.family<ExamsController, BuildContext>(
        (ref, context) {
  final examsRepo = ref.watch(examRepoProvider);

  return ExamsController(examsRepo: examsRepo, context);
});

// *  Get Exams Data By Month ============================================
final getExamsDataByMonthProvider =
    FutureProvider.family<List<ExamModel>, (BuildContext, String date)>(
        (ref, params) async {
  final examsCtrl = ref.watch(examsControllerProvider(params.$1));

  return await examsCtrl.getExamsByMonth(
    date: params.$2,
  );
});

//? =======================================================================
class ExamsController extends BaseVM {
  final ExamsRepo examsRepo;
  final BuildContext context;

  ExamsController(this.context, {required this.examsRepo});

  //? Get Exams ----------------------------
  Future<List<ExamModel>> getExams() async {
    return await baseFunction(context, () async {
      final exams = await examsRepo.getExams();

      return exams;
    });
  }

  //? Get Exams By Month ----------------------------
  Future<List<ExamModel>> getExamsByMonth({required String date}) async {
    return await baseFunction(context, () async {
      final exams = await examsRepo.getExamsByMonth(date: date);

      return exams;
    });
  }

//? Add Exam --------------------------------------
  Future<void> addExam({
    required TextEditingController questionController,
    required ValueNotifier<DateTime> selectedDate,
  }) async {
    return await baseFunction(context, () async {
      //   final examModel = ExamModel(
      //     date: selectedDate.value.formatDateToString,
      //     classModel: selectedTeacherClass.value,
      //     teacher: TeacherModel(
      //       id: const UserModel().currentUser.id,
      //     ),
      //     question: questionController.text,
      //   );
      //
      //   await examsRepo.addExam(examModel: examModel);
      //
      //   if (!context.mounted) return;
      //   context.back();
      //   context.toReplacement(const ExamScreen());
      //   context.showBarMessage(context.tr.addedSuccessfully);
    });
  }

  //? Edit Exam --------------------------------------
  Future<void> updateStudentRate({
    required ExamModel exam,
  }) async {
    return await baseFunction(
      context,
      () async {
        await examsRepo.editExam(examModel: exam);
        if (!context.mounted) return;
        context.back();
        context.toReplacement(const ExamScreen());
        context.showBarMessage(context.tr.editSuccessfully);
      },
      additionalFunction: (context) => getExams(),
    );
  } //? Delete Exam -----------------------------------

  Future<void> deleteExam({required int id}) async {
    return await baseFunction(
      context,
      () async {
        await examsRepo.deleteExam(id: id);
        if (!context.mounted) return;
        context.back();
        context.toReplacement(const ExamScreen());
        context.showBarMessage(context.tr.deletedSuccessfully, isError: true);
      },
      additionalFunction: (context) => getExams(),
    );
  }

  void clearData(
      {required TextEditingController questionController,
      required ValueNotifier<DateTime> selectedDate,
      required WidgetRef ref}) {
    selectedDate.value = DateTime.now();
    questionController.clear();
  }
}
