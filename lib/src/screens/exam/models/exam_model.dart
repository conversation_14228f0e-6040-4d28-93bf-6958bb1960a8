import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:equatable/equatable.dart';

import '../../../shared/data/remote/api_strings.dart';

List<ExamModel> responseToExamModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final exams = data.map((e) => ExamModel.fromJson(e)).toList();

  return exams;
}

class ExamModel extends Equatable {
  final int? id;
  final String? createdAt;
  final String date;
  final ClassModel? classModel;
  final TeacherModel? teacher;
  final String question;
  final List<StudentsResultModel> studentsResult;

  const ExamModel({
    this.id,
    this.createdAt,
    this.date = '',
    this.question = '',
    this.classModel,
    this.teacher,
    this.studentsResult = const [],
  });

  //? From Json
  factory ExamModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];

    return ExamModel(
      id: json[ApiStrings.id],
      date: attributes[ApiStrings.date] ?? '',
      createdAt: attributes[ApiStrings.createdAt] ?? '',
      studentsResult: attributes[ApiStrings.studentsResult] != null
          ? (attributes[ApiStrings.studentsResult] as List)
              .map((e) => StudentsResultModel.fromJson(e))
              .toList()
          : [],
      question: attributes[ApiStrings.question] ?? '',
      teacher: attributes[ApiStrings.teacher] != null &&
              attributes[ApiStrings.teacher].containsKey(ApiStrings.data)
          ? TeacherModel.fromAttributesJson(
              attributes[ApiStrings.teacher][ApiStrings.data],
            )
          : TeacherModel.fromJson(attributes[ApiStrings.teacher]),
      // classModel: attributes[ApiStrings.classString] != null
      //     ? ClassModel.fromJson(
      //         attributes[ApiStrings.classString][ApiStrings.data])
      //     : null,
    );
  }

  //? From Json Without Attributes
  factory ExamModel.fromJsonWithoutAttributes(Map<String, dynamic> json) {
    return ExamModel(
      id: json[ApiStrings.id],
      date: json[ApiStrings.date] ?? '',
      createdAt: json[ApiStrings.createdAt] ?? '',
      studentsResult: json[ApiStrings.studentsResult] != null
          ? (json[ApiStrings.studentsResult] as List)
              .map((e) => StudentsResultModel.fromJsonWithoutAttributes(e))
              .toList()
          : [],
      question: json[ApiStrings.question] ?? '',
      teacher: json[ApiStrings.teacher] != null
          ? TeacherModel.fromJson(json[ApiStrings.teacher])
          : null,
      classModel: json[ApiStrings.classString] != null
          ? ClassModel.fromJsonWithOutAttributes(json[ApiStrings.classString])
          : null,
    );
  }

  //? To Json ---------------------------------------------------
  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      ApiStrings.date: date,
      ApiStrings.classString: classModel?.id,
      ApiStrings.teacher: teacher?.id,
      ApiStrings.question: question,
      ApiStrings.studentsResult: studentsResult.map((e) => e.toJson()).toList(),
    };
  }

  //? Copy With ---------------------------------------------------
  ExamModel copyWith({
    int? id,
    String? createdAt,
    String? date,
    ClassModel? classModel,
    TeacherModel? teacher,
    String? question,
    List<StudentsResultModel>? studentsResult,
  }) {
    return ExamModel(
      id: id ?? this.id,
      createdAt: createdAt ?? this.createdAt,
      date: date ?? this.date,
      classModel: classModel ?? this.classModel,
      teacher: teacher ?? this.teacher,
      question: question ?? this.question,
      studentsResult: studentsResult ?? this.studentsResult,
    );
  }

  @override
  List<Object?> get props => [id, date, question, classModel, teacher];
}

class StudentsResultModel {
  final StudentModel? student;
  final num rate;
  final String note;

  const StudentsResultModel({
    required this.student,
    this.rate = 0,
    this.note = '',
  });

  //? From Json
  factory StudentsResultModel.fromJson(Map<String, dynamic> json) {
    return StudentsResultModel(
      student: json[ApiStrings.student][ApiStrings.data] != null
          ? StudentModel.fromJson(json[ApiStrings.student][ApiStrings.data])
          : null,
      rate: json[ApiStrings.rate] == null || json[ApiStrings.rate] == 0
          ? 1
          : json[ApiStrings.rate],
      note: json[ApiStrings.note] ?? '',
    );
  }

  factory StudentsResultModel.fromJsonWithoutAttributes(
      Map<String, dynamic> json) {
    return StudentsResultModel(
      student: json[ApiStrings.student] != null
          ? StudentModel.fromJsonWithoutAttributes(json[ApiStrings.student])
          : null,
      rate: json[ApiStrings.rate] == null || json[ApiStrings.rate] == 0
          ? 1
          : json[ApiStrings.rate],
      note: json[ApiStrings.note] ?? '',
    );
  }

  //? To Json ---------------------------------------------------
  Map<String, dynamic> toJson() {
    return {
      if (student != null) ApiStrings.student: student!.id,
      ApiStrings.rate: rate,
      ApiStrings.note: note,
    };
  }
}
