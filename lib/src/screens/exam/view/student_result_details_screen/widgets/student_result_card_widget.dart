import 'package:connectify_app/src/screens/exam/models/exam_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class StudentResultCardWidget extends ConsumerWidget {
  final ExamModel exam;
  final int number;
  final StudentsResultModel? studentResult;

  const StudentResultCardWidget({
    super.key,
    required this.exam,
    required this.number,
    required this.studentResult,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return BaseContainer(
        padding: AppSpaces.appbarPadding,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                //! Exam Number
                Text(
                  '$number.',
                  style: context.blueHint.copyWith(fontWeight: FontWeight.bold),
                ),

                context.smallGap,

                //! Exam (Name - Date)
                Text(
                  exam.question,
                  style: context.blueHint.copyWith(fontWeight: FontWeight.bold),
                ),
              ],
            ),
            context.mediumGap,
            Center(
              child: RatingBar.builder(
                initialRating: studentResult?.rate.toDouble() ?? 1.0,
                minRating: 1,
                direction: Axis.horizontal,
                allowHalfRating: true,
                ignoreGestures: true,
                itemCount: 5,
                itemSize: 40,
                itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
                itemBuilder: (context, _) => const Icon(
                  Icons.star,
                  color: Colors.amber,
                ),
                onRatingUpdate: (ratingData) {
                  // Read-only for parent app
                },
              ),
            ),
            context.mediumGap,
            if (studentResult?.note != null && studentResult!.note.isNotEmpty)
              Text(
                '${context.tr.note}: ${studentResult!.note}',
                style: context.labelMedium,
              )
          ],
        ));
  }
}
