import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/exam/models/exam_model.dart';
import 'package:connectify_app/src/screens/exam/view/widgets/exam_tab_bar/exam_selected_screen/results_tab_screen/widgets/result_card_widget.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../home/<USER>/main_screen/widgets/main_app_bar.dart';

class ResultHomeWidget extends HookConsumerWidget {
  final List<ExamModel> exams;

  const ResultHomeWidget({
    super.key,
    required this.exams,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ResultCardWidget(
      questions: exams,
      student: StudentModel(
        id: UserModel.studentId(),
        name: selectedStudent.value?.name ?? const UserModel().currentUser.name,
      ),
    ).paddingAll(
      AppSpaces.mediumPadding,
    );
  }
}
