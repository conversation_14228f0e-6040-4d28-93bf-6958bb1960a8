import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/exam/models/exam_model.dart';
import 'package:connectify_app/src/screens/exam/view/student_result_details_screen/student_result_details_screen.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_rating_bar/flutter_rating_bar.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class ResultCardWidget extends ConsumerWidget {
  final List<ExamModel> questions;
  final StudentModel student;

  const ResultCardWidget({
    super.key,
    required this.questions,
    required this.student,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final studentAverageRate = questions
        .map((e) =>
            e.studentsResult
                .firstWhereOrNull(
                    (element) => element.student?.id == student.id)
                ?.rate ??
            1.0)
        .toList();

    final calculateRate = studentAverageRate.isNotEmpty
        ? studentAverageRate.fold(0.0, (prev, element) => prev + element) /
            studentAverageRate.length
        : 0.0;

    return BaseContainer(
        padding: AppSpaces.appbarPadding,
        onTap: () => context.to(StudentResultDetailsScreen(
              student: student,
            )),
        child: Row(
          children: [
            //! Student Name
            Expanded(
              child: Text(
                context.isEng
                    ? '${student.name}\n${DateTime.now().formatToMonthName} ${context.tr.results}'
                    : 'نتائج ${student.name} ل${DateTime.now().formatToMonthName}',
                style: context.blueHint.copyWith(fontWeight: FontWeight.bold),
              ),
            ),

            context.mediumGap,

            //! Rating
            RatingBar.builder(
              initialRating: calculateRate.toDouble(),
              minRating: 1,
              direction: Axis.horizontal,
              allowHalfRating: true,
              ignoreGestures: true,
              itemCount: 5,
              itemSize: 17,
              itemPadding: const EdgeInsets.symmetric(horizontal: 4.0),
              itemBuilder: (context, _) => const Icon(
                Icons.star,
                color: Colors.amber,
              ),
              onRatingUpdate: (rating) {},
            ),

            context.mediumGap,

            //arrow right
            const Icon(
              Icons.arrow_forward_ios,
              size: 17,
            ),
          ],
        ).paddingOnly(
          right: AppSpaces.smallPadding,
        ));
  }
}
