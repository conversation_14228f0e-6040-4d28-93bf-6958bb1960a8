import 'package:connectify_app/src/screens/exam/controllers/exam_tab_bar_controller.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class ExamTabBarWidget extends ConsumerWidget {
  const ExamTabBarWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final examTabBarCtrl = ref.watch(examTabBarController);
    final examChangeNotifierCtrl = ref.watch(examTabBarControllerProvider);

    final List<String> tabs = [
      context.tr.results,
      context.tr.exams,
    ];

    return Container(
      margin: const EdgeInsets.all(AppSpaces.smallPadding),
      padding: const EdgeInsets.only(
        left: AppSpaces.smallPadding,
        right: AppSpaces.smallPadding,
        bottom: AppSpaces.smallPadding,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(10),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            spreadRadius: 2,
            blurRadius: 5,
            offset: const Offset(0, 3),
          ),
        ],
      ),
      child: DefaultTabController(
        initialIndex: examChangeNotifierCtrl,
        length: tabs.length,
        child: TabBar(
          indicatorSize: TabBarIndicatorSize.label,
          tabAlignment: TabAlignment.center,
          onTap: (index) {
            examTabBarCtrl.changeIndex(index);
          },
          indicatorColor: Colors.transparent,
          indicator: const BoxDecoration(
            color: ColorManager.primaryColor,
            borderRadius: BorderRadius.all(Radius.circular(10)),
          ),
          dividerColor: ColorManager.grey,
          dividerHeight: 0,
          unselectedLabelColor: ColorManager.darkGrey.withOpacity(0.5),
          isScrollable: false,
          tabs: tabs
              .map((e) => ClipRRect(
                    borderRadius: BorderRadius.circular(10),
                    child: Container(
                      padding: EdgeInsets.symmetric(
                        horizontal: context.width * 0.03,
                        vertical: 9.h,
                      ),
                      child: FittedBox(
                        fit: BoxFit.scaleDown,
                        alignment: Alignment.center,
                        child: Text(
                          e,
                          style: examChangeNotifierCtrl == tabs.indexOf(e)
                              ? context.whiteLabelLarge
                              : context.hint,
                        ),
                      ),
                    ),
                  ))
              .toList(),
        ),
      ).paddingOnly(
        top: AppSpaces.mediumPadding,
      ),
    );
  }
}
