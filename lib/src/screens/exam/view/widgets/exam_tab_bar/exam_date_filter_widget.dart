import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class ExamDateFilterWidget extends StatelessWidget {
  final Function() onNext;
  final Function() onPrevious;
  final DateTime date;

  const ExamDateFilterWidget({
    super.key,
    required this.onNext,
    required this.onPrevious,
    required this.date,
  });

  @override
  Widget build(BuildContext context) {
    final monthName = date.formatToMonthName;

    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        IconButton(
            onPressed: () => onPrevious(),
            icon: const Icon(Icons.arrow_back_ios_rounded)),
        context.mediumGap,
        Text(
          monthName,
          style: context.greyLabelLarge,
          textAlign: TextAlign.center,
        ),
        context.mediumGap,
        IconButton(
            onPressed: () => onNext(),
            icon: const Icon(Icons.arrow_forward_ios_rounded)),
      ],
    ).paddingSymmetric(horizontal: AppSpaces.mediumPadding);
  }
}
