import 'package:connectify_app/src/screens/food/controller/food_controller.dart';
import 'package:connectify_app/src/screens/food/view/widgets/add_food_widgets.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

import '../../../../shared/widgets/shared_widgets.dart';

class AddFoodDialog extends StatelessWidget {
  const AddFoodDialog({super.key});

  @override
  Widget build(BuildContext context) {
    return const SizedBox();
  }
}

Future<void> showAddFoodDialog(context) {
  return showDialog(
    context: context,
    builder: (context) {
      return HookConsumer(
        builder: (context, ref, child) {
          final foodChangeNotifierCtrl =
              ref.watch(foodChangeNotifierControllerProvider(context));

          final valueNotifiers = {
            ApiStrings.student: useState<StudentModel?>(null),
            ApiStrings.food: useState<int>(0),
            ApiStrings.type: useState<int>(0),
          };

          //!-----------------------------------------------------

          final formKey = useState(GlobalKey<FormState>());

          //!-----------------------------------------------------

          return AlertDialogWidget(
              header: context.tr.food,
              isLoading: foodChangeNotifierCtrl.isLoading,
              isImage: false,
              child: Form(
                key: formKey.value,
                child: AddFoodWidgets(
                  valueNotifiers: valueNotifiers,
                ),
              ),
              onConfirm: () {});
        },
      );
    },
  );
}
