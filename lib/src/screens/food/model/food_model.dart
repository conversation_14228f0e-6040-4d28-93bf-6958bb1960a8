import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:equatable/equatable.dart';

import '../../../shared/shared_models/base_model.dart';

List<FoodModel> responseToFoodModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final foodData = data.map((e) => FoodModel.fromJson(e)).toList();

  return foodData;
}

List<MealModel> responseToMealsModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final foodData = data.map((e) => MealModel.fromJson(e)).toList();

  return foodData;
}

enum MealTypes { breakFast, snack, lunch }

enum MealAmount { all, more, some, none }

class FoodModel extends Equatable {
  final int? id;
  final MealTypes? mealType;
  final MealAmount? mealAmount;
  final String meal;
  final DateTime? createdAt;

  const FoodModel({
    this.id,
    this.mealType,
    this.mealAmount,
    this.createdAt,
    this.meal = '',
  });

  factory FoodModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];
    return FoodModel(
      id: json[ApiStrings.id],
      mealAmount: getMealAmountFromApi(
          attributes[ApiStrings.mealAmount].toString().toLowerCase()),
      mealType: getMealTypeFromApi(
          attributes[ApiStrings.mealType].toString().toLowerCase()),
      createdAt: DateTime.parse(attributes[ApiStrings.createdAt]).toLocal(),
    );
  }

  static MealTypes getMealTypeFromApi(String value) {
    switch (value) {
      case 'breakfast':
        return MealTypes.breakFast;
      case 'snack':
        return MealTypes.snack;
      case 'lunch':
        return MealTypes.lunch;

      default:
        return MealTypes.breakFast;
    }
  }

  static MealAmount getMealAmountFromApi(String value) {
    switch (value) {
      case 'all':
        return MealAmount.all;
      case 'more':
        return MealAmount.more;
      case 'some':
        return MealAmount.some;
      case 'none':
        return MealAmount.none;

      default:
        return MealAmount.all;
    }
  }

  String getMealType() {
    switch (mealType) {
      case MealTypes.breakFast:
        return "Breakfast";
      case MealTypes.snack:
        return "Snack";
      case MealTypes.lunch:
        return "Lunch";

      default:
        return 'Breakfast';
    }
  }

  static MealTypes getMealTypeValue({required int value}) {
    switch (value) {
      case 0:
        return MealTypes.breakFast;
      case 1:
        return MealTypes.snack;
      case 2:
        return MealTypes.lunch;

      default:
        return MealTypes.breakFast;
    }
  }

  String getMealAmount() {
    switch (mealAmount) {
      case MealAmount.all:
        return "All";
      case MealAmount.more:
        return "More";
      case MealAmount.some:
        return "Some";
      case MealAmount.none:
        return "None";

      default:
        return 'All';
    }
  }

  static MealAmount getMealAmountValue({required int value}) {
    switch (value) {
      case 0:
        return MealAmount.all;
      case 1:
        return MealAmount.more;
      case 2:
        return MealAmount.some;

      default:
        return MealAmount.none;
    }
  }

  @override
  List<Object?> get props => [id, mealType, mealAmount];
}

enum WeekDays { Saturday, Sunday, Monday, Tuesday, Wednesday, Thursday, Friday }

class MealModel extends BaseModel {
  final MealTypes? mealType;
  final WeekDays? day;

  const MealModel({
    super.id,
    super.name,
    this.mealType,
    this.day,
  });

  factory MealModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];
    return MealModel(
      id: json[ApiStrings.id],
      name: attributes[ApiStrings.meal],
      mealType: MealModel.getMealTypeValue(attributes[ApiStrings.type]),
      day: MealModel.getWeekDayValue(attributes[ApiStrings.day]),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      ApiStrings.meal: name,
      ApiStrings.type: mealType == MealTypes.breakFast
          ? "Breakfast"
          : mealType == MealTypes.snack
              ? "Snack"
              : "Lunch",
      ApiStrings.day: day?.name,
    };
  }

  static MealTypes getMealTypeValue(String value) {
    switch (value.toLowerCase()) {
      case 'breakfast':
        return MealTypes.breakFast;
      case 'snack':
        return MealTypes.snack;
      case 'lunch':
        return MealTypes.lunch;
      default:
        return MealTypes.breakFast;
    }
  }

  static WeekDays getWeekDayValue(String value) {
    switch (value.toLowerCase()) {
      case 'monday':
        return WeekDays.Monday;
      case 'tuesday':
        return WeekDays.Tuesday;
      case 'wednesday':
        return WeekDays.Wednesday;
      case 'thursday':
        return WeekDays.Thursday;
      case 'friday':
        return WeekDays.Friday;
      case 'saturday':
        return WeekDays.Saturday;
      case 'sunday':
        return WeekDays.Sunday;
      default:
        return WeekDays.Monday;
    }
  }
}
