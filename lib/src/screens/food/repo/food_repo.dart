import 'package:connectify_app/src/screens/food/model/food_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

// * =========================================================

final foodRepoProvider = Provider((ref) {
  final networkApiServices = ref.watch(networkServiceProvider);

  return FoodRepo(networkApiServices);
});

//? ========================================================

class FoodRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  FoodRepo(this._networkApiServices);

//? get Food Data ========================================================
  Future<List<MealModel>> getMeals() async {
    return await baseFunction(() async {
      final response = await _networkApiServices.getResponse(ApiEndpoints.meal);

      final foodData = await compute(responseToMealsModelList, response);

      return foodData;
    });
  }

  Future<List<FoodModel>> getFoodDataByDay({required DateTime date}) async {
    return await baseFunction(() async {
      final response = await _networkApiServices.getResponse(
          '${ApiEndpoints.food}&${ApiEndpoints.filterByDate(date)}');

      final foodData = await compute(responseToFoodModelList, response);

      return foodData;
    });
  }
}
