import 'package:connectify_app/src/screens/food/repo/food_repo.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../model/food_model.dart';

final foodControllerProvider =
    Provider.family<FoodController, BuildContext>((ref, context) {
  final foodRepo = ref.watch(foodRepoProvider);

  return FoodController(foodRepo: foodRepo, context: context);
});
final foodChangeNotifierControllerProvider =
    ChangeNotifierProvider.family<FoodController, BuildContext>((ref, context) {
  final foodRepo = ref.watch(foodRepoProvider);

  return FoodController(foodRepo: foodRepo, context: context);
});
final getFoodByDayData =
    FutureProvider.family<List<FoodModel>, (BuildContext, DateTime)>(
        (ref, params) {
  final foodRepo = ref.watch(foodRepoProvider);
  final context = params.$1;

  final foodController = FoodController(foodRepo: foodRepo, context: context);

  final date = params.$2;

  return foodController.getFoodDataByDay(date: date);
});

final getMealsProvider =
    FutureProvider.family<List<MealModel>, BuildContext>((ref, context) {
  final foodRepo = ref.watch(foodRepoProvider);

  final foodController = FoodController(foodRepo: foodRepo, context: context);

  return foodController.getMeals();
});

class FoodController extends BaseVM {
  final FoodRepo foodRepo;
  final BuildContext context;

  FoodController({required this.foodRepo, required this.context});

  Future<List<MealModel>> getMeals() async {
    return await baseFunction(context, () async {
      final foodData = await foodRepo.getMeals();

      return foodData;
    });
  }

  Future<List<FoodModel>> getFoodDataByDay({required DateTime date}) async {
    return await baseFunction(context, () async {
      final foodData = await foodRepo.getFoodDataByDay(date: date);

      return foodData;
    });
  }
}
