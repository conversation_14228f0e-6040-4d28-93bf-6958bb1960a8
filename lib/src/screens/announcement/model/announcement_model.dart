import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:equatable/equatable.dart';

List<AnnouncementModel> responseToAnnouncementModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final announcements = data.map((e) => AnnouncementModel.fromJson(e)).toList();

  return announcements;
}

enum AnnouncementTarget {
  admins,
  teachers,
  parents,
  all,
}

extension AnnouncementTargetExtension on AnnouncementTarget {
  String get displayName {
    switch (this) {
      case AnnouncementTarget.admins:
        return 'Admins';
      case AnnouncementTarget.teachers:
        return 'Teachers';
      case AnnouncementTarget.parents:
        return 'Parents';
      case AnnouncementTarget.all:
        return 'All';
    }
  }

  String get displayNameAr {
    switch (this) {
      case AnnouncementTarget.admins:
        return 'المسؤولين';
      case AnnouncementTarget.teachers:
        return 'المعلمين';
      case AnnouncementTarget.parents:
        return 'الأهالي';
      case AnnouncementTarget.all:
        return 'الجميع';
    }
  }

  String get value {
    switch (this) {
      case AnnouncementTarget.admins:
        return 'admins';
      case AnnouncementTarget.teachers:
        return 'teachers';
      case AnnouncementTarget.parents:
        return 'parents';
      case AnnouncementTarget.all:
        return 'all';
    }
  }

  static AnnouncementTarget fromString(String value) {
    switch (value) {
      case 'admins':
        return AnnouncementTarget.admins;
      case 'teachers':
        return AnnouncementTarget.teachers;
      case 'parents':
        return AnnouncementTarget.parents;
      case 'all':
        return AnnouncementTarget.all;
      default:
        return AnnouncementTarget.all;
    }
  }
}

class AnnouncementModel extends Equatable {
  final int? id;
  final String title;
  final String description;
  final AnnouncementTarget target;
  final DateTime? createdAt;

  const AnnouncementModel({
    this.id,
    this.title = '',
    this.description = '',
    this.target = AnnouncementTarget.all,
    this.createdAt,
  });

  factory AnnouncementModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];

    return AnnouncementModel(
      id: json[ApiStrings.id],
      title: attributes[ApiStrings.title] ?? '',
      description: attributes[ApiStrings.description] ?? '',
      target: AnnouncementTargetExtension.fromString(
          attributes[ApiStrings.target] ?? 'all'),
      createdAt: attributes[ApiStrings.createdAt] != null
          ? DateTime.parse(attributes[ApiStrings.createdAt]).toLocal()
          : null,
    );
  }

  factory AnnouncementModel.fromJsonWithoutAttributes(
      Map<String, dynamic> json) {
    return AnnouncementModel(
      id: json[ApiStrings.id],
      title: json[ApiStrings.title] ?? '',
      description: json[ApiStrings.description] ?? '',
      target: AnnouncementTargetExtension.fromString(
          json[ApiStrings.target] ?? 'all'),
      createdAt: json[ApiStrings.createdAt] != null
          ? DateTime.parse(json[ApiStrings.createdAt]).toLocal()
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      ApiStrings.title: title,
      ApiStrings.description: description,
      ApiStrings.target: target.value,
      ApiStrings.admin: const UserModel().currentUser.id,
      ApiStrings.nursery: NurseryModelHelper.currentNurseryId(),
    };
  }

  AnnouncementModel copyWith({
    int? id,
    String? title,
    String? description,
    AnnouncementTarget? target,
    DateTime? createdAt,
  }) {
    return AnnouncementModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      target: target ?? this.target,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        target,
        createdAt,
      ];
}
