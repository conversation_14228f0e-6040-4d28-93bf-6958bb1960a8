import 'package:connectify_app/src/screens/announcement/model/announcement_model.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class AnnouncementCard extends StatelessWidget {
  final AnnouncementModel announcement;

  const AnnouncementCard({
    super.key,
    required this.announcement,
  });

  @override
  Widget build(BuildContext context) {
    return BaseContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with title and actions
          Row(
            children: [
              if (announcement.createdAt != null)
                Expanded(
                  child: Text(
                    announcement.createdAt!.formatDateToTimeAndString,
                    style: context.labelSmall.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              context.mediumGap,

              // Target badge
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getTargetColor(announcement.target),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      context.isEng
                          ? announcement.target.displayName
                          : announcement.target.displayNameAr,
                      style: context.labelSmall.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                ],
              ),
            ],
          ),

          context.mediumGap,

          Text(
            announcement.title,
            style: context.title.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),

          context.mediumGap,

          // Description
          Text(
            announcement.description,
            style: context.labelLarge,
          ),
        ],
      ),
    );
  }

  Color _getTargetColor(AnnouncementTarget target) {
    switch (target) {
      case AnnouncementTarget.admins:
        return Colors.red;
      case AnnouncementTarget.teachers:
        return Colors.blue;
      case AnnouncementTarget.parents:
        return Colors.green;
      case AnnouncementTarget.all:
        return Colors.purple;
    }
  }
}
