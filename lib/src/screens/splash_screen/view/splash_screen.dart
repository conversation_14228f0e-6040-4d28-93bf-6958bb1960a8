import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/auth/view/sign_in_screen/sign_in_screen.dart';
import 'package:connectify_app/src/screens/onboarding/controllers/onboarding_controller.dart';
import 'package:connectify_app/src/screens/onboarding/models/onboarding_model.dart';
import 'package:connectify_app/src/screens/onboarding/view/onboarding_screen.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class SplashScreen extends HookConsumerWidget {
  final bool fromLogout;

  const SplashScreen({super.key, this.fromLogout = false});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final onBoardingCtrl = ref.watch(onBoardingController(context));
    final onBoardingPage = useState<List<OnBoardingModel>>([]);

    void getOnBoardingData() async {
      final onBoardingPages = await onBoardingCtrl.getOnBoardingData();
      onBoardingPage.value = onBoardingPages;
    }

    final haveSeenOnBoarding =
        GetStorageService.getLocalData(key: LocalKeys.haveSeenOnBoarding) ??
            false;

    useEffect(() {
      if (!haveSeenOnBoarding) {
        getOnBoardingData();

        GetStorageService.setLocalData(
          key: LocalKeys.haveSeenOnBoarding,
          value: true,
        );

        // * Navigate to On Boarding after 3 seconds
        Future.delayed(const Duration(seconds: 3), () {
          context.toReplacement(OnBoardingScreen(
            onBoardingPages: onBoardingPage.value,
          ));
        });
      } else {
        Future.delayed(const Duration(seconds: 3), () {
          context.toReplacement(const SignInScreen());
        });
      }

      return;
    }, []);

    return Scaffold(
      body: Center(
        child: Image.asset(
          Assets.imagesSplash,
          height: context.height,
          width: context.width,
          fit: BoxFit.cover,
        ),
      ),
    );
  }
}
