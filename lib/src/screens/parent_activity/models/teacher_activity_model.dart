import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model.dart';
import 'package:connectify_app/src/screens/parent_activity/models/activity_model.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/shared/shared_models/base_media_model.dart';
import 'package:connectify_app/src/shared/shared_models/base_model.dart';

import '../../../shared/data/remote/api_strings.dart';

List<TeacherActivityModel> responseToTeacherActivityModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final activities = data.map((e) => TeacherActivityModel.fromJson(e)).toList();

  return activities;
}

class TeacherActivityModel extends BaseModel {
  final ClassModel? classModel;
  final NurseryModel? nursery;
  final TeacherModel? teacher;
  final ActivityModel? activity;
  final String startTime;
  final String endTime;
  final String day;
  final String date;
  final DateTime? createdAtDate;

  final bool isWeekly;
  final List<ActivityNoteModel> notes;

  const TeacherActivityModel({
    super.id,
    super.image,
    this.createdAtDate,
    this.nursery,
    this.classModel,
    this.teacher,
    this.activity,
    this.day = '',
    this.date = '',
    this.startTime = '',
    this.endTime = '',
    this.isWeekly = true,
    this.notes = const [],
  });

  //? From Json
  factory TeacherActivityModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];

    final activity = attributes[ApiStrings.activity] == null
        ? null
        : attributes[ApiStrings.activity].containsKey(ApiStrings.data) &&
                attributes[ApiStrings.activity][ApiStrings.data] != null
            ? ActivityModel.fromJson(
                attributes[ApiStrings.activity][ApiStrings.data])
            : ActivityModel.fromJsonWithoutAttributes(
                attributes[ApiStrings.activity]);

    final classModel = attributes[ApiStrings.classString] == null
        ? null
        : attributes[ApiStrings.classString].containsKey(ApiStrings.data) &&
                attributes[ApiStrings.classString][ApiStrings.data] != null
            ? ClassModel.fromJson(
                attributes[ApiStrings.classString][ApiStrings.data])
            : ClassModel.fromJsonWithOutAttributes(
                attributes[ApiStrings.classString]);

    final notes = attributes[ApiStrings.activityNotes] != null
        ? List<ActivityNoteModel>.from(attributes[ApiStrings.activityNotes]
            .map((e) => ActivityNoteModel.fromJson(e)))
        : <ActivityNoteModel>[];

    return TeacherActivityModel(
        id: json[ApiStrings.id],
        day: attributes[ApiStrings.day] ?? '',
        startTime: attributes[ApiStrings.from] ?? '',
        endTime: attributes[ApiStrings.to] ?? '',
        classModel: classModel,
        date: attributes[ApiStrings.date] ?? '',
        createdAtDate: DateTime.parse(attributes[ApiStrings.createdAt] ?? ''),
        isWeekly: attributes[ApiStrings.isWeekly] ?? true,
        notes: notes,
        activity: activity);
  }

  //? From Json Without Attributes
  factory TeacherActivityModel.fromJsonWithoutAttributes(
      Map<String, dynamic> json) {
    final activity = json[ApiStrings.activity] != null
        ? ActivityModel.fromJsonWithoutAttributes(json[ApiStrings.activity])
        : null;

    final classModel = json[ApiStrings.classString] != null
        ? ClassModel.fromJsonWithOutAttributes(json[ApiStrings.classString])
        : null;

    final teacher = json[ApiStrings.teacher] != null
        ? TeacherModel.fromJson(json[ApiStrings.teacher])
        : null;

    final notes = json[ApiStrings.activityNotes] != null
        ? List<ActivityNoteModel>.from(json[ApiStrings.activityNotes]
            .map((e) => ActivityNoteModel.fromJsonWithoutAttributes(e)))
        : <ActivityNoteModel>[];

    return TeacherActivityModel(
        id: json[ApiStrings.id],
        day: json[ApiStrings.day] ?? '',
        startTime: json[ApiStrings.from] ?? '',
        endTime: json[ApiStrings.to] ?? '',
        classModel: classModel,
        date: json[ApiStrings.date] ?? '',
        createdAtDate: json[ApiStrings.createdAt] != null
            ? DateTime.parse(json[ApiStrings.createdAt])
            : null,
        isWeekly:
            json['is_weekly_activity'] ?? json[ApiStrings.isWeekly] ?? true,
        notes: notes,
        teacher: teacher,
        activity: activity);
  }

//? To Json ---------------------------------------------------
  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      if (day.isNotEmpty) ApiStrings.day: day,
      if (date.isNotEmpty) ApiStrings.date: date,
      if (startTime.isNotEmpty) ApiStrings.from: startTime,
      if (endTime.isNotEmpty) ApiStrings.to: endTime,
      ApiStrings.teacher: const UserModel().currentUser.id,
      // ApiStrings.classString: const UserModel().currentUser.classModel?.id,
      if (activity != null) ApiStrings.activity: activity?.id,
      ApiStrings.isWeekly: isWeekly,
      ApiStrings.activityNotes: notes.map((e) => e.toJson()).toList(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        image,
        createdAt,
        nursery,
        classModel,
        teacher,
        activity,
        startTime,
        endTime,
        day,
        date,
        isWeekly,
        notes,
      ];
}

class ActivityNoteModel {
  final String date;
  final String note;
  final List<BaseMediaModel> media;

  ActivityNoteModel({
    this.date = '',
    this.note = '',
    this.media = const [],
  });

  factory ActivityNoteModel.fromJson(Map<String, dynamic> json) {
    return ActivityNoteModel(
      date: json[ApiStrings.date] ?? '',
      note: json[ApiStrings.note] ?? '',
      media: json[ApiStrings.media] == null
          ? <BaseMediaModel>[]
          : json[ApiStrings.media] is List
              ? (json[ApiStrings.media] as List)
                  .map((e) => BaseMediaModel.fromJson(
                        e,
                      ))
                  .toList()
              : (json[ApiStrings.media].containsKey(ApiStrings.data) &&
                      json[ApiStrings.media][ApiStrings.data] != null)
                  ? (json[ApiStrings.media][ApiStrings.data] as List)
                      .map((e) => BaseMediaModel.fromJson(
                            e[ApiStrings.attributes],
                          ))
                      .toList()
                  : <BaseMediaModel>[],
      // json[ApiStrings.media] != null &&
      //         (json[ApiStrings.media].containsKey(ApiStrings.data) &&
      //             json[ApiStrings.media][ApiStrings.data] != null)
      //     ? (json[ApiStrings.media][ApiStrings.data] as List)
      //         .map((e) => BaseMediaModel.fromJson(
      //               e[ApiStrings.attributes],
      //             ))
      //         .toList()
      //     : <BaseMediaModel>[],
    );
  }

  factory ActivityNoteModel.fromJsonWithoutAttributes(
      Map<String, dynamic> json) {
    return ActivityNoteModel(
      date: json[ApiStrings.date] ?? '',
      note: json[ApiStrings.note] ?? '',
      media: json[ApiStrings.media] == null
          ? <BaseMediaModel>[]
          : json[ApiStrings.media] is List
              ? (json[ApiStrings.media] as List)
                  .map((e) => BaseMediaModel.fromJson(e))
                  .toList()
              : <BaseMediaModel>[],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      ApiStrings.date: date,
      ApiStrings.note: note,
      ApiStrings.media: media.map((e) => e.id).toList(),
    };
  }
}
