// * Class Provider Controller ========================================
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:xr_helper/xr_helper.dart';

import '../models/teacher_activity_model.dart';
import '../repos/teacher_activities_repo.dart';

List<String> weekDays = [
  'Monday',
  'Tuesday',
  'Wednesday',
  'Thursday',
  'Friday',
  'Saturday',
  'Sunday'
];

final teacherActivityProviderController =
    Provider.family<TeacherActivityController, BuildContext>((ref, context) {
  final teacherActivityRepo = ref.watch(teacherActivityRepoProvider);

  return TeacherActivityController(context,
      teacherActivityRepo: teacherActivityRepo);
});

final teacherActivityChangeNotifierProvider =
    ChangeNotifierProvider.family<TeacherActivityController, BuildContext>(
        (ref, context) {
  final teacherActivityRepo = ref.watch(teacherActivityRepoProvider);

  return TeacherActivityController(context,
      teacherActivityRepo: teacherActivityRepo);
});

// * Get Activities Data ========================================
final getTeacherActivitiesDataProvider =
    FutureProvider.family<List<TeacherActivityModel>, BuildContext>(
        (ref, context) async {
  final activityCtrl = ref.watch(teacherActivityProviderController(context));

  return await activityCtrl.getTeacherActivitiesData();
});

final getTeacherActivityByDateProvider =
    FutureProvider.family<List<TeacherActivityModel>, (BuildContext, DateTime)>(
        (ref, params) {
  final activityCtrl = ref.watch(teacherActivityProviderController(params.$1));

  final date = params.$2;

  return activityCtrl.getTeacherActivityByDate(date: date);
});

// * Get Activities Data By Today ========================================
final getTeacherActivitiesByCurrentTimeDataProvider =
    FutureProvider.family<List<TeacherActivityModel>, BuildContext>(
        (ref, context) async {
  final activityCtrl = ref.watch(teacherActivityProviderController(context));
  DateTime now = DateTime.now();
  final activities = await activityCtrl.getTeacherActivitiesByDay(date: now);

  return activities.where((element) {
    final format = DateFormat('HH:mm', 'en');
    final startTime = format.parse(element.startTime);
    final endTime = format.parse(element.endTime);

    // Adjust the date part of startTime and endTime to match the current date
    final startTimeDate = DateTime(
        now.year, now.month, now.day, startTime.hour, startTime.minute);
    final endTimeDate =
        DateTime(now.year, now.month, now.day, endTime.hour, endTime.minute);

    return now.isAfter(startTimeDate) && now.isBefore(endTimeDate);
  }).toList();
});

// * Get Activities Data By Today ========================================
final getTeacherActivitiesByTodayDataProvider =
    FutureProvider.family<List<TeacherActivityModel>, BuildContext>(
        (ref, context) async {
  final activityCtrl = ref.watch(teacherActivityProviderController(context));
  DateTime now = DateTime.now();

  final activities = await activityCtrl.getTeacherActivitiesByDay(date: now);

  return activities;
});

// * getTeacherActivitiesByDate
final getTeacherActivitiesByDateProvider = FutureProvider.family<
    List<TeacherActivityModel>,
    (BuildContext, String date, String day)>((ref, params) async {
  final activityCtrl = ref.watch(teacherActivityProviderController(params.$1));

  return await activityCtrl.getTeacherActivitiesByDate(
    dateFormat: params.$2,
    dayFormat: params.$3,
  );
});

class TeacherActivityController extends BaseVM {
  final BuildContext context;
  final TeacherActivityRepo teacherActivityRepo;

  TeacherActivityController(this.context, {required this.teacherActivityRepo});

  //? Get Activities Data ------------------------------------------
  Future<List<TeacherActivityModel>> getTeacherActivitiesData() async {
    return await baseFunction(
      context,
      () async {
        final activities = await teacherActivityRepo.getActivities();

        return activities;
      },
    );
  }

  //? Get Activities Data ------------------------------------------
  Future<List<TeacherActivityModel>> getTeacherActivitiesByDay({
    required DateTime date,
  }) async {
    return await baseFunction(
      context,
      () async {
        final activities =
            await teacherActivityRepo.getActivitiesByDay(date: date);

        return activities;
      },
    );
  }

  Future<List<TeacherActivityModel>> getTeacherActivityByDate(
      {required DateTime date}) async {
    return await baseFunction(
      context,
      () async {
        final activities =
            await teacherActivityRepo.getActivitiesByDay(date: date);

        return activities;
      },
    );
  }

  Future<List<TeacherActivityModel>> getTeacherActivitiesByDate({
    required String dayFormat,
    required String dateFormat,
  }) async {
    return await baseFunction(
      context,
      () async {
        final activities = await teacherActivityRepo.getActivitiesByDate(
          dateFormat: dateFormat,
          dayFormat: dayFormat,
        );

        return activities;
      },
    );
  }

  void onNext({required ValueNotifier<int> selectedIndex}) {
    selectedIndex.value = (selectedIndex.value + 1) % weekDays.length;
  }

  void onPrev({required ValueNotifier<int> selectedIndex}) {
    selectedIndex.value =
        (selectedIndex.value - 1 + weekDays.length) % weekDays.length;
  }
}
