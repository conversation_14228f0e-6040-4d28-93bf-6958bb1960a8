// * Class Provider Controller ========================================
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../shared/data/remote/api_strings.dart';
import '../models/activity_model.dart';
import '../repos/activities_repo.dart';

final activityProviderController =
    Provider.family<ActivityController, BuildContext>((ref, context) {
  final activityRepo = ref.watch(activityRepoProvider);

  return ActivityController(context, activityRepo: activityRepo);
});

final activityChangeNotifierProvider =
    ChangeNotifierProvider.family<ActivityController, BuildContext>(
        (ref, context) {
  final activityRepo = ref.watch(activityRepoProvider);

  return ActivityController(context, activityRepo: activityRepo);
});

// * Get Activities Data ========================================
final getActivitiesDataProvider =
    FutureProvider.family<List<ActivityModel>, BuildContext>(
        (ref, context) async {
  final activityCtrl = ref.watch(activityProviderController(context));

  return await activityCtrl.getActivitiesData();
});

//?==============================================================
class ActivityController extends BaseVM {
  final BuildContext context;
  final ActivityRepo activityRepo;

  ActivityController(this.context, {required this.activityRepo});

  //? Get Activities Data ------------------------------------------
  Future<List<ActivityModel>> getActivitiesData() async {
    return await baseFunction(
      context,
      () async {
        final activities = await activityRepo.getActivities();

        return activities;
      },
    );
  }

//? Get Activities Data ------------------------------------------
  Future<void> addActivity(
      {required Map<String, TextEditingController> controllers,
      required String pickedImage}) async {
    return await baseFunction(context, () async {
      final activity = ActivityModel(
        name: controllers[ApiStrings.name]!.text,
        description: controllers[ApiStrings.description]!.text,
      );

      await activityRepo.addActivity(
        activity: activity,
        pickedImage: pickedImage,
      );

      getActivitiesData();
    });
  }

  //? Edit Activity Data ------------------------------------------
  Future<void> editActivity(
      {required Map<String, TextEditingController> controllers,
      required int id,
      required String pickedImage}) async {
    return await baseFunction(context, () async {
      final activity = ActivityModel(
        name: controllers[ApiStrings.name]!.text,
        description: controllers[ApiStrings.description]!.text,
      );

      await activityRepo.editActivity(
          activity: activity, id: id, pickedImage: pickedImage);
    }, additionalFunction: (context) => getActivitiesData());
  }

//? Delete Activity Data ------------------------------------------
  Future<void> deleteActivity({required int id}) async {
    return await baseFunction(context, () async {
      await activityRepo.deleteActivity(id: id);
      getActivitiesData();
    });
  }
}
