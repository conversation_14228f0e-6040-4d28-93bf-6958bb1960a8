import 'package:connectify_app/src/screens/parent_activity/models/parent_activities_response_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

// * =========================================================

final parentActivitiesRepoProvider = Provider((ref) {
  final networkApiServices = ref.watch(networkServiceProvider);
  return ParentActivitiesRepo(networkApiServices);
});

class ParentActivitiesRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  ParentActivitiesRepo(this._networkApiServices);

  //? Get Parent Activities Data ========================================================
  Future<ParentActivitiesResponseModel> getParentActivitiesData({
    Map<String, dynamic>? filters,
    String? sort,
  }) async {
    return await baseFunction(() async {
      final url = ApiEndpoints.parentActivities(
        filters: filters,
        sort: sort,
      );

      final response = await _networkApiServices.getResponse(url);

      final parentActivitiesData =
          ParentActivitiesResponseModel.fromJson(response);

      return parentActivitiesData;
    });
  }

  //? Get Parent Activities Data by Date ========================================================
  Future<ParentActivitiesResponseModel> getParentActivitiesByDate({
    required DateTime date,
    String? dayName,
  }) async {
    return await baseFunction(() async {
      final filters = <String, dynamic>{};

      // Add date filters
      final dateString =
          date.toIso8601String().split('T')[0]; // YYYY-MM-DD format
      final startOfDay = DateTime(date.year, date.month, date.day, 0, 0, 0);
      final endOfDay = DateTime(date.year, date.month, date.day, 23, 59, 59);

      // For teacher activities, we need both day and date filters
      if (dayName != null) {
        filters['\$or'] = [
          {'day': dayName},
          {'date': dateString}
        ];
      }

      // For other activities, use createdAt date range
      filters['createdAt'] = {
        '\$gte': startOfDay.toIso8601String(),
        '\$lte': endOfDay.toIso8601String(),
      };

      final url = ApiEndpoints.parentActivities(
        filters: filters,
        sort: 'createdAt:desc',
      );

      final response = await _networkApiServices.getResponse(url);

      final parentActivitiesData =
          ParentActivitiesResponseModel.fromJson(response);

      return parentActivitiesData;
    });
  }
}
