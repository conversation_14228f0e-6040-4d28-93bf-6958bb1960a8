import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:intl/intl.dart';
import 'package:riverpod/riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../models/teacher_activity_model.dart';

final teacherActivityRepoProvider = Provider<TeacherActivityRepo>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return TeacherActivityRepo(networkApiService);
});

class TeacherActivityRepo with BaseRepository {
  final BaseApiServices _networkApiService;

  TeacherActivityRepo(this._networkApiService);

  //? Get Activity Data ------------------------------------------
  Future<List<TeacherActivityModel>> getActivities() async {
    return baseFunction(
      () async {
        final response = await _networkApiService
            .getResponse(ApiEndpoints.teacherActivities);

        final activityData =
            compute(responseToTeacherActivityModelList, response);

        return activityData;
      },
    );
  }

  Future<List<TeacherActivityModel>> getActivitiesByDay({
    required DateTime date,
  }) async {
    String dateFormat = DateFormat('EEEE', 'en').format(date);
    final formattedDate = date.formatDateToString;

    return baseFunction(
      () async {
        final response = await _networkApiService.getResponse(
          '${ApiEndpoints.teacherActivities}&filters[\$or][0][day]=$dateFormat&filters[\$or][1][date]=$formattedDate',
        );

        final activityData =
            compute(responseToTeacherActivityModelList, response);

        return activityData;
      },
    );
  }

  Future<List<TeacherActivityModel>> getActivitiesByDate({
    required String dayFormat,
    required String dateFormat,
  }) async {
    return baseFunction(
      () async {
        // Construct the API endpoint with filters
        final response = await _networkApiService.getResponse(
          '${ApiEndpoints.teacherActivities}&filters[\$or][0][day]=$dayFormat&filters[\$or][1][date]=$dateFormat',
        );

        // Process the response using compute for potential offloading
        final activityData =
            compute(responseToTeacherActivityModelList, response);

        return activityData;
      },
    );
  }

// Future<List<TeacherActivityModel>> getActivitiesByDate(
//     {required DateTime date}) async {
//   return baseFunction(
//     () async {
//       final response = await _networkApiService.getResponse(
//         '${ApiEndpoints.teacherActivities}&filters[date]=${date.formatDateToString}',
//       );
//
//       final activityData =
//           compute(responseToTeacherActivityModelList, response);
//
//       return activityData;
//     },
//   );
// }
}
