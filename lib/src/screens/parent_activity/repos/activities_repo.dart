import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:riverpod/riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../models/activity_model.dart';

final activityRepoProvider = Provider<ActivityRepo>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return ActivityRepo(networkApiService);
});

class ActivityRepo with BaseRepository {
  final BaseApiServices _networkApiService;

  ActivityRepo(this._networkApiService);

  //? Get Activity Data ------------------------------------------
  Future<List<ActivityModel>> getActivities() async {
    return baseFunction(
      () async {
        final response =
            await _networkApiService.getResponse(ApiEndpoints.activities);

        final activityData = compute(responseToActivityModelList, response);

        return activityData;
      },
    );
  }

//? Add Activity Data ------------------------------------------

  Future<void> addActivity(
      {required ActivityModel activity, required String pickedImage}) async {
    return await baseFunction(() async {
      return await _networkApiService.postResponse(ApiEndpoints.activities,
          body: activity.toJson(), filePaths: [pickedImage]);
    });
  }

  //? Edit Activity Data ------------------------------------------
  Future<void> editActivity({
    required ActivityModel activity,
    required int id,
    required String pickedImage,
  }) async {
    return await baseFunction(() async {
      await _networkApiService.putResponse(
        '${ApiEndpoints.editDeleteActivities}/$id',
        data: activity.toJson(),
        filePaths: [pickedImage],
      );
    });
  }

//? Delete Activity Data ------------------------------------------
  Future<void> deleteActivity({required int id}) async {
    return await baseFunction(() async {
      await _networkApiService
          .deleteResponse('${ApiEndpoints.editDeleteActivities}/$id');
    });
  }
}
