import 'dart:developer';
import 'dart:io';
import 'dart:typed_data';

import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/shared_models/base_media_model.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:dio/dio.dart';
import 'package:flutter/material.dart';
import 'package:gal/gal.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:video_thumbnail/video_thumbnail.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../shared/services/media/controller/media_controller.dart';
import '../../../../shared/widgets/video_player_screen.dart';

class TeacherActivityMediaWidget extends HookConsumerWidget {
  final List<BaseMediaModel> mediaList;

  const TeacherActivityMediaWidget({
    super.key,
    required this.mediaList,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaController = ref.watch(mediaPickerControllerProvider);

    return ListView.separated(
      padding: const EdgeInsets.all(AppSpaces.mediumPadding),
      itemCount: mediaList.length,
      separatorBuilder: (context, index) => context.mediumGap,
      itemBuilder: (context, index) {
        final mediaItem = mediaList[index];
        final isNetworkMedia = mediaItem.url?.startsWith('http') ?? false;
        final isVideo = _isVideoFile(mediaItem.url ?? '');

        return ClipRRect(
          borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
          child: SizedBox(
            height: 150,
            child: Stack(
              children: [
                // Display video thumbnail or image
                isVideo
                    ? Stack(
                        children: [
                          _buildVideoThumbnail(
                              mediaItem, isNetworkMedia, mediaController),

                          // center icon video play
                          Positioned.fill(
                            child: Center(
                              child: IconButton(
                                icon: const Icon(
                                  Icons.play_circle_filled,
                                  color: Colors.white,
                                  size: 64,
                                ),
                                onPressed: () {
                                  // Navigate to video player screen
                                  Navigator.of(context).push(
                                    MaterialPageRoute(
                                      builder: (context) => VideoPlayerScreen(
                                        videoUrl: mediaItem.url ?? '',
                                        isNetworkVideo: isNetworkMedia,
                                      ),
                                    ),
                                  );
                                },
                              ),
                            ),
                          ),
                        ],
                      )
                    : _buildImageWidget(mediaItem, isNetworkMedia),
                Positioned(
                  right: 0,
                  bottom: 0,
                  child: IconButton(
                    icon: const CircleAvatar(
                      backgroundColor: ColorManager.blueColor,
                      child: Icon(
                        Icons.download,
                        color: Colors.white,
                      ),
                    ),
                    onPressed: () async {
                      try {
                        final extension = mediaItem.url?.split('.').last ?? '';
                        final path =
                            '${Directory.systemTemp.path}/${mediaItem.publicId}.$extension';

                        log('FFFFF ${path}');

                        await Dio().download(mediaItem.url ?? '', path);

                        if (isVideo) {
                          await Gal.putVideo(path);
                        } else {
                          await Gal.putImage(path);
                        }

                        context
                            .showBarMessage(context.tr.downloadedSuccessfully);
                      } catch (e) {
                        Log.e('Error downloading media: $e');
                        context.showBarMessage(context.tr.errorOccurred,
                            isError: true);
                      }

                      // _flutterMediaDownloaderPlugin.downloadMedia(
                      //   context,
                      //   mediaItem.url ?? '',
                      // );
                    },
                  ),
                ),
                // view eye icon for full screen image
                Positioned(
                  right: 0,
                  top: 0,
                  child: IconButton(
                    icon: const CircleAvatar(
                      backgroundColor: ColorManager.primaryColor,
                      child: Icon(
                        Icons.remove_red_eye,
                        color: Colors.white,
                      ),
                    ),
                    onPressed: () {
                      if (isVideo) {
                        // Navigate to video player screen
                        Navigator.of(context).push(
                          MaterialPageRoute(
                            builder: (context) => VideoPlayerScreen(
                              videoUrl: mediaItem.url ?? '',
                              isNetworkVideo: isNetworkMedia,
                            ),
                          ),
                        );
                      } else {
                        // Show image dialog
                        showDialog(
                          context: context,
                          builder: (context) => Dialog(
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(
                                  AppRadius.baseContainerRadius),
                            ),
                            child: ClipRRect(
                              borderRadius: BorderRadius.circular(
                                  AppRadius.baseContainerRadius),
                              child:
                                  _buildImageWidget(mediaItem, isNetworkMedia),
                            ),
                          ),
                        );
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  bool _isVideoFile(String url) {
    final videoExtensions = ['.mp4', '.mov', '.avi', '.mkv', '.webm', '.m4v'];
    return videoExtensions.any((ext) => url.toLowerCase().endsWith(ext));
  }

  Widget _buildVideoThumbnail(BaseMediaModel mediaItem, bool isNetworkMedia,
      MediaPickerController mediaController) {
    // For network videos, try to generate thumbnail
    if (isNetworkMedia && mediaItem.url != null) {
      return FutureBuilder<Uint8List?>(
        future: _generateNetworkVideoThumbnail(mediaItem.url!),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return _buildVideoPlaceholder(isLoading: true);
          }
          if (snapshot.hasData && snapshot.data != null) {
            return Image.memory(
              snapshot.data!,
              fit: BoxFit.cover,
              width: double.infinity,
              height: double.infinity,
            );
          }
          return _buildVideoPlaceholder();
        },
      );
    }

    // Fallback to a placeholder
    return _buildVideoPlaceholder();
  }

  Widget _buildVideoPlaceholder({bool isLoading = false}) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      color: Colors.grey[300],
      child: isLoading
          ? const Center(
              child: CircularProgressIndicator(
                strokeWidth: 2,
              ),
            )
          : const Icon(
              Icons.videocam,
              size: 48,
              color: Colors.grey,
            ),
    );
  }

  Future<Uint8List?> _generateNetworkVideoThumbnail(String videoUrl) async {
    try {
      return await VideoThumbnail.thumbnailData(
        video: videoUrl,
        imageFormat: ImageFormat.JPEG,
        quality: 75,
      );
    } catch (e) {
      Log.e('Error generating network video thumbnail: $e');
      return null;
    }
  }

  Widget _buildImageWidget(BaseMediaModel mediaItem, bool isNetworkMedia) {
    return isNetworkMedia
        ? Image.network(
            mediaItem.url ?? '',
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
            errorBuilder: (context, error, stackTrace) => const BaseCachedImage(
              AppConsts.activityPlaceholder,
            ),
          )
        : Image.file(
            File(mediaItem.url ?? ''),
            fit: BoxFit.cover,
            width: double.infinity,
            height: double.infinity,
          );
  }
}
