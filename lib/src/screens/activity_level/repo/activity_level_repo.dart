import 'package:connectify_app/src/screens/activity_level/model/activity_level_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

// * =========================================================

final activityLevelRepoProvider = Provider((ref) {
  final networkApiServices = ref.watch(networkServiceProvider);

  return ActivityLevelRepo(networkApiServices);
});

//? ========================================================

class ActivityLevelRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  ActivityLevelRepo(this._networkApiServices);

//? get Activity Level Data ========================================================
  Future<List<ActivityLevelModel>> getActivityLevelData() async {
    return await baseFunction(() async {
      final response =
          _networkApiServices.getResponse(ApiEndpoints.activityLevel);

      final activityLevelData =
          await compute(responseToActivityLevelModelList, response);

      return activityLevelData;
    });
  }

  Future<List<ActivityLevelModel>> getActivityLevelDataByDay(
      {required DateTime date}) async {
    return await baseFunction(() async {
      final response = await _networkApiServices.getResponse(
          '${ApiEndpoints.activityLevel}&${ApiEndpoints.filterByDate(date)}');

      final activityLevelData =
          await compute(responseToActivityLevelModelList, response);

      return activityLevelData;
    });
  }
}
