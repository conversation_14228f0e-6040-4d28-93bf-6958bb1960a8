import 'package:connectify_app/src/screens/activity_level/repo/activity_level_repo.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../model/activity_level_model.dart';

final activityLevelControllerProvider =
    Provider.family<ActivityLevelController, BuildContext>((ref, context) {
  final activityLevelRepo = ref.watch(activityLevelRepoProvider);

  return ActivityLevelController(
      activityLevelRepo: activityLevelRepo, context: context);
});

final activityLevelChangeNotifierControllerProvider =
    ChangeNotifierProvider.family<ActivityLevelController, BuildContext>(
        (ref, context) {
  final activityLevelRepo = ref.watch(activityLevelRepoProvider);

  return ActivityLevelController(
      activityLevelRepo: activityLevelRepo, context: context);
});

final getAllActivityLevelData =
    FutureProvider.family<List<ActivityLevelModel>, BuildContext>(
        (ref, context) {
  final activityLevelRepo = ref.watch(activityLevelRepoProvider);

  final activityLevelController = ActivityLevelController(
      activityLevelRepo: activityLevelRepo, context: context);

  return activityLevelController.getActivityLevelData();
});

final getActivityLevelByDayData =
    FutureProvider.family<List<ActivityLevelModel>, (BuildContext, DateTime)>(
        (ref, params) {
  final activityLevelRepo = ref.watch(activityLevelRepoProvider);
  final context = params.$1;

  final activityLevelController = ActivityLevelController(
      activityLevelRepo: activityLevelRepo, context: context);

  final date = params.$2;

  return activityLevelController.getActivityLevelDataByDay(date: date);
});

class ActivityLevelController extends BaseVM {
  final ActivityLevelRepo activityLevelRepo;
  final BuildContext context;

  ActivityLevelController(
      {required this.activityLevelRepo, required this.context});

  Future<List<ActivityLevelModel>> getActivityLevelData() async {
    return await baseFunction(context, () async {
      final activityLevelData = await activityLevelRepo.getActivityLevelData();

      return activityLevelData;
    });
  }

  Future<List<ActivityLevelModel>> getActivityLevelDataByDay(
      {required DateTime date}) async {
    return await baseFunction(context, () async {
      final activityLevelData =
          await activityLevelRepo.getActivityLevelDataByDay(date: date);

      return activityLevelData;
    });
  }
}
