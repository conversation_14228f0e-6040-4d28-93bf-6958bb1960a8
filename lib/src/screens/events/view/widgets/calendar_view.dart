import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../controllers/event_controller.dart';
import '../../models/event_model.dart';

CalendarFormat _calendarFormat = CalendarFormat.month;

class CalendarView extends HookConsumerWidget {
  final List<EventModel> events;
  final ValueNotifier<DateTime> selectedDay;

  const CalendarView(
      {super.key, required this.events, required this.selectedDay});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _focusedDay = useState(DateTime.now());

    print('events: $events');
    return StatefulBuilder(builder: (context, setState) {
      return TableCalendar(
        firstDay: DateTime.utc(2010, 10, 16),
        lastDay: DateTime.utc(2030, 3, 14),
        focusedDay: _focusedDay.value,
        calendarFormat: _calendarFormat,
        selectedDayPredicate: (day) {
          return isSameDay(selectedDay.value, day);
        },
        onDaySelected: (day, focusedDay) {
          if (!isSameDay(selectedDay.value, day)) {
            setState(() {
              selectedDay.value = day;
              _focusedDay.value = focusedDay;
            });

            ref.refresh(filteredEventsProvider((context, selectedDay.value)));
            ref.refresh(allEventsDataProvider(context));
          }
        },
        onFormatChanged: (format) {
          if (_calendarFormat != format) {
            setState(() {
              _calendarFormat = format;
            });
          }
        },
        onPageChanged: (focusedDay) {
          _focusedDay.value = focusedDay;
        },
        calendarStyle: CalendarStyle(
          isTodayHighlighted: true,
          selectedDecoration:
              AppConsts.eventBoxDecoration(ColorManager.primaryColor),
          markerSize: 4,
          defaultTextStyle: textTheme(context).labelLarge!,
          weekendTextStyle: textTheme(context).labelLarge!,
          todayDecoration: AppConsts.eventBoxDecoration(ColorManager.lightBlue),
          defaultDecoration: AppConsts.eventBoxDecoration(),
          weekendDecoration: AppConsts.eventBoxDecoration(),
          markerDecoration: AppConsts.markerBoxDecoration(),
          markerMargin: EdgeInsets.symmetric(
              horizontal: 1,
              vertical: _calendarFormat == CalendarFormat.month ? 5 : 4.5),
        ),
        eventLoader: (day) {
          return events.where((event) {
            return isSameDay(event.startTime!, day);
          }).toList();
        },
      );
    });
  }
}
