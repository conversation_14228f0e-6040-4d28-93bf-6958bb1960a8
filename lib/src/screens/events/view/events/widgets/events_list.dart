import 'package:connectify_app/src/screens/events/models/event_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'event_card.dart';

class EventsList extends ConsumerWidget {
  final List<EventModel> events;

  const EventsList({
    super.key,
    required this.events,
  });


  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return ListView(
      shrinkWrap: true,
      physics: const BouncingScrollPhysics(),
      padding: const EdgeInsets.symmetric(
        vertical: 12,
      ),
      children: List.generate(
        events.length,
        (index) => EventCard(event: events[index]),
      ),
    );
  }
}
