import 'dart:developer';

import 'package:connectify_app/src/screens/events/models/event_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:riverpod/riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

final eventRepoProvider = Provider<EventRepo>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return EventRepo(networkApiService);
});

//? ===========================================================
class EventRepo with BaseRepository {
  final BaseApiServices _networkApiService;

  EventRepo(this._networkApiService);

  //? Get Event Data ------------------------------------------
  Future<List<EventModel>> getEvents({
    DateTime? date,
  }) async {
    return baseFunction(
      () async {
        final filter =
            date != null ? '?filter[start_time]=${date.toIso8601String()}' : '';

        log('asfasf $filter');
        final response =
            await _networkApiService.getResponse(ApiEndpoints.events);

        final eventPages = compute(responseToEventModelList, response);

        return eventPages;
      },
    );
  }

  //? Add Event Data ------------------------------------------
  Future<dynamic> addEvent(
      {required EventModel eventModel, required String pickedImage}) async {
    return await baseFunction(() async {
      return await _networkApiService.postResponse(
        ApiEndpoints.events,
        body: eventModel.toJson(),
        filePaths: [pickedImage],
        fieldName: ApiStrings.logo,
      );
    });
  }

  //? Edit Event Data ------------------------------------------
  Future<void> editEvent({
    required EventModel eventModel,
    required int? id,
    required String pickedImage,
  }) async {
    return await baseFunction(() async {
      await _networkApiService.putResponse(
        '${ApiEndpoints.editDeleteEvent}/$id',
        data: eventModel.toJson(),
        filePaths: [pickedImage],
        fieldName: ApiStrings.logo,
      );
    });
  }

//? Delete Event Data ------------------------------------------
  Future<void> deleteEvent({required int id}) async {
    return await baseFunction(() async {
      await _networkApiService
          .deleteResponse('${ApiEndpoints.editDeleteEvent}/$id');
    });
  }
}
