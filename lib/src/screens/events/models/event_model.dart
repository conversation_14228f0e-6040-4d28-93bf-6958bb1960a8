import 'dart:developer';

import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:equatable/equatable.dart';
import 'package:xr_helper/xr_helper.dart';

List<EventModel> responseToEventModelList(response) {
  final data = (response[ApiStrings.data] as List?) ?? [];

  final classes = data.map((e) => EventModel.fromJson(e)).toList();

  return classes;
}

class EventModel extends Equatable {
  final int? id;
  final String? name;
  final String? description;
  final DateTime? startTime;
  final List<ClassModel>? classes;
  final List<TeacherModel>? teachers;

  const EventModel({
    this.id,
    this.name,
    this.description,
    this.startTime,
    this.classes,
    this.teachers,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        startTime,
        classes,
        teachers,
      ];

  factory EventModel.fromJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];

    // final classModel =
    //     attributes[ApiStrings.classString][ApiStrings.data] != null
    //         ? ClassModel.fromJson(
    //             attributes[ApiStrings.classString][ApiStrings.data])
    //         : null;

    log('asfasrrrfsa ${attributes}');

    final teachers = attributes[ApiStrings.teachers] != null &&
            attributes[ApiStrings.teachers][ApiStrings.data] != null
        ? (attributes[ApiStrings.teachers][ApiStrings.data] as List)
            .map((e) => TeacherModel.fromAttributesJson(e))
            .toList()
        : null;

    Log.w('asfasrrrfsa ${attributes[ApiStrings.selectedClasses]}');
    final classes = attributes[ApiStrings.selectedClasses] != null &&
            attributes[ApiStrings.selectedClasses][ApiStrings.data] != null
        ? (attributes[ApiStrings.selectedClasses][ApiStrings.data] as List)
            .map((e) => ClassModel.fromJson(e))
            .toList()
        : null;

    return EventModel(
      id: json[ApiStrings.id],
      name: attributes[ApiStrings.name],
      description: attributes[ApiStrings.description],
      startTime: DateTime.parse(attributes[ApiStrings.startTime]),
      classes: classes,
      teachers: teachers,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      if (id != null) ApiStrings.id: id,
      ApiStrings.name: name,
      ApiStrings.description: description,
      ApiStrings.startTime: startTime.formatDateToTimeAndString,
      ApiStrings.selectedClasses: classes?.map((e) => e.id).toList(),
      ApiStrings.teachers: teachers?.map((e) => e.id).toList(),
    };
  }
}
