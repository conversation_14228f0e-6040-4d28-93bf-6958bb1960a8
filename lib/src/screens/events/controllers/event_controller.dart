import 'package:connectify_app/src/screens/events/models/event_model.dart';
import 'package:connectify_app/src/screens/events/view/events/events_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../shared/data/remote/api_strings.dart';
import '../../../shared/services/media/controller/media_controller.dart';
import '../repos/event_repo.dart';

// * Event Provider Controller ========================================
final eventProviderController =
    Provider.family<EventController, BuildContext>((ref, context) {
  final eventRepo = ref.watch(eventRepoProvider);

  return EventController(context, eventRepo: eventRepo);
});

// * Change Notifier Event Controller ========================================
final eventChangeNotifierController =
    ChangeNotifierProvider.family<EventController, BuildContext>(
        (ref, context) {
  final eventRepo = ref.watch(eventRepoProvider);

  return EventController(context, eventRepo: eventRepo);
});

// * Get Event Data ========================================
final allEventsDataProvider =
    FutureProvider.family<List<EventModel>, BuildContext>((ref, context) async {
  final eventCtrl = ref.watch(eventProviderController(context));

  return await eventCtrl.getEvents();
});

// * Get Filtered Event Data ========================================
final filteredEventsProvider =
    FutureProvider.family<List<EventModel>, (BuildContext, DateTime)>(
        (ref, params) async {
  final context = params.$1;
  final date = params.$2;

  final eventCtrl = ref.watch(eventProviderController(context));

  final events = await eventCtrl.getEvents(
    date: date,
  );

  final filteredEvents = events.where((event) {
    return event.startTime!.day == date.day &&
        event.startTime!.month == date.month &&
        event.startTime!.year == date.year;
  }).toList();

  return filteredEvents;
});

//? ==========================================================================
class EventController extends BaseVM {
  final BuildContext context;
  final EventRepo eventRepo;

  EventController(this.context, {required this.eventRepo});

//? Get Events ----------------------------------------------------
  Future<List<EventModel>> getEvents({
    DateTime? date,
  }) async {
    return await baseFunction(
      context,
      () async {
        final events = await eventRepo.getEvents(
          date: date,
        );

        events.sort(
          (a, b) => b.startTime!.compareTo(a.startTime!),
        );

        return events;
      },
    );
  }

//? Add Events ----------------------------------------------------
  Future<void> addEvent(
      {required Map<String, TextEditingController> controllers,
      required Map<String, ValueNotifier> valueNotifiers,
      required String pickedImage}) async {
    return await baseFunction(context, () async {
      final eventModel = EventModel(
        name: controllers[ApiStrings.eventName]!.text,
        description: controllers[ApiStrings.description]!.text,
        classes: valueNotifiers[ApiStrings.classString]!.value,
        startTime: valueNotifiers[ApiStrings.date]!.value,
        teachers: valueNotifiers[ApiStrings.teachers]!.value,
      );

      await eventRepo.addEvent(
        eventModel: eventModel,
        pickedImage: pickedImage,
      );

      if (context.mounted) {
        context.back();
        context.toReplacement(const EventsScreen());
        context.showBarMessage(context.tr.addedSuccessfully);
      }
    }, additionalFunction: (context) => getEvents());
  }

//? Edit Event Data ------------------------------------------
  Future<void> editEvent(
      {required Map<String, TextEditingController> controllers,
      required Map<String, ValueNotifier> valueNotifiers,
      required int? id,
      required String pickedImage}) async {
    return await baseFunction(context, () async {
      final eventModel = EventModel(
        name: controllers[ApiStrings.eventName]!.text,
        description: controllers[ApiStrings.description]!.text,
        classes: valueNotifiers[ApiStrings.classString]!.value,
        startTime: valueNotifiers[ApiStrings.date]!.value,
        teachers: valueNotifiers[ApiStrings.teachers]!.value,
      );

      await eventRepo.editEvent(
          id: id, eventModel: eventModel, pickedImage: pickedImage);

      if (context.mounted) {
        context.back();
        context.toReplacement(const EventsScreen());
        context.showBarMessage(context.tr.editSuccessfully);
      }
    }, additionalFunction: (context) => getEvents());
  }

//? Delete Event Data ------------------------------------------
  Future<void> deleteEvent({required int id}) async {
    return await baseFunction(context, () async {
      await eventRepo.deleteEvent(id: id);

      if (!context.mounted) return;
      context.toReplacement(const EventsScreen());
      context.showBarMessage(context.tr.deletedSuccessfully, isError: true);
    }, additionalFunction: (_) => getEvents());
  }

  void clearData(
      {required Map<String, TextEditingController> controllers,
      required WidgetRef ref}) {
    controllers[ApiStrings.name]!.clear();
    controllers[ApiStrings.description]!.clear();
    ref.watch(mediaPickerControllerProvider).clearFiles();
  }
}
