import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:flutter/material.dart';

import '../../../../shared/widgets/shared_widgets.dart';

class StudentDetailsScreen extends StatelessWidget {
  final StudentModel student;

  const StudentDetailsScreen({super.key, required this.student});

  @override
  Widget build(BuildContext context) {
    final isFatherPhone =
        student.parentPhoneNumber!.isEmpty || student.parentPhoneNumber == null;
    return Scaffold(
      appBar: MainAppBar(
        isBackButton: true,
        title: student.name,
      ),
      body: Column(
        children: [
          DetailsTopSectionWidget(
            imagePath: student.image?.url ?? '',
            name: student.name,
            description: student.classModel?.name ?? '',
          ),
        ],
      ),
    );
  }
}
