import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
import 'package:connectify_app/src/screens/student/view/student_screen/widgets/students_grid_view.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../home/<USER>/main_screen.dart';

class StudentsScreen extends ConsumerWidget {
  const StudentsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final students = ref.watch(getAllStudentsProvider(context));
    return WillPopScope(
      onWillPop: () async {
        context.toReplacement(const MainScreen());
        return false;
      },
      child: Safe<PERSON><PERSON>(
        child: Scaffold(
          appBar: MainAppBar(
            title: context.tr.students,
            isBackButton: true,
          ),
          body: students.get(data: (student) {
            Log.f(student.firstOrNull?.name);
            return StudentGridView(
              navigateWidget: const StudentsScreen(),
              students: student,
            );
          }),
        ),
      ),
    );
  }
}
