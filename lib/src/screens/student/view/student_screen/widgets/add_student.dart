import 'package:connectify_app/src/screens/class/view/classes_screen/classes_screen.dart';
import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
import 'package:connectify_app/src/screens/student/models/student_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../generated/assets.dart';
import '../../../../../shared/services/media/controller/media_controller.dart';
import '../../../../../shared/widgets/shared_widgets.dart';
import '../../../../class/models/class_model.dart';

class AddStudentDialog extends HookConsumerWidget {
  final bool fromClassDetails;
  final Widget navigateWidget;

  const AddStudentDialog(
      {super.key,
      this.fromClassDetails = false,
      this.navigateWidget = const ClassesScreen()});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaController = ref.watch(mediaPickerControllerProvider);

    return const AddSquareWidget(
      isStudent: true,
    ).onTap(() {
      showAddStudentDialog(context,
              navigateWidget: navigateWidget,
              fromClassDetails: fromClassDetails,
              ref: ref)
          .then((value) {
        mediaController.clearFiles();
      });
    });
  }
}

Future<void> showAddStudentDialog(
  BuildContext context, {
  required Widget navigateWidget,
  StudentModel? student,
  bool fromClassDetails = true,
  required WidgetRef ref,
}) async {
  final isEdit = student != null;

  showDialog(
    context: context,
    builder: (context) {
      return HookConsumer(
        builder: (context, ref, child) {
          final controllers = {
            ApiStrings.name: useTextEditingController(text: student?.name),
            ApiStrings.homeAddress:
                useTextEditingController(text: student?.homeAddress),
            ApiStrings.motherPhoneNumber:
                useTextEditingController(text: student?.motherPhoneNumber),
            ApiStrings.parentPhoneNumber:
                useTextEditingController(text: student?.parentPhoneNumber),
            ApiStrings.fees:
                useTextEditingController(text: student?.fees?.toString()),
          };

          final valueNotifiers = {
            ApiStrings.classString: useState<ClassModel?>(null),
            ApiStrings.birthDate: useState<DateTime?>(student?.birthDate),
            ApiStrings.gender: useState<String>(student?.gender ?? 'male'),
            ApiStrings.pickupPersons:
                useState<List<String>>(student?.pickupPersons ?? <String>[]),
          };

          final pickupControllers = useState<List<TextEditingController>>(
            student?.pickupPersons
                    ?.map((e) => useTextEditingController(text: e))
                    .toList() ??
                [],
          );

          final isParentPhoneAdded = useState(
              student?.parentPhoneNumber != null &&
                  (student?.parentPhoneNumber!.isNotEmpty ?? false));

          final formKey = useState(GlobalKey<FormState>());

          void clearData() {
            controllers[ApiStrings.name]!.clear();
            controllers[ApiStrings.homeAddress]!.clear();
            controllers[ApiStrings.motherPhoneNumber]!.clear();
            controllers[ApiStrings.parentPhoneNumber]!.clear();

            valueNotifiers[ApiStrings.classString]!.value = null;
            valueNotifiers[ApiStrings.birthDate]!.value = null;

            isParentPhoneAdded.value = false;

            ref.watch(mediaPickerControllerProvider).clearFiles();
          }

          final studentChangeNotifier =
              ref.watch(studentChangeNotifierProvider(context));

          final filePath = ref.watch(mediaPickerControllerProvider).filePath;

          final focusNode = useFocusNode();

          Future<void> addEditStudent() async {
            await studentChangeNotifier.editStudent(
              id: student!.id!,
              controllers: controllers,
              valueNotifiers: valueNotifiers,
              navigateWidget: navigateWidget,
              pickupControllers: pickupControllers.value,
            );
          }

          return PopScope(
            onPopInvoked: (didPop) =>
                ref.watch(mediaPickerControllerProvider).clearFiles(),
            child: AlertDialogWidget(
              networkImage: student?.image?.url ?? '',
              iconPath: Assets.iconsAdd,
              header: context.tr.addNewStudents,
              isLoading: studentChangeNotifier.isLoading,isImage: false,
              child: Form(
                key: formKey.value,
                child: Column(
                  children: [
                    ...pickupControllers.value.asMap().entries.map((entry) {
                      final index = entry.key;
                      final controller = entry.value;
                      final isLastItem =
                          index == pickupControllers.value.length - 1;

                      final isAddedBefore =
                          student?.pickupPersons?.contains(controller.text) ??
                              false;

                      return Padding(
                        padding: const EdgeInsets.symmetric(vertical: 8.0),
                        child: Row(
                          children: [
                            Expanded(
                              child: BaseTextField(
                                enabled: !isAddedBefore,
                                focusNode: isLastItem ? focusNode : null,
                                controller: controller,
                                isRequired: false,
                                label: context.tr.pickupPerson,
                                hint: context.tr.enterPickupPerson,
                                onChanged: (value) {
                                  // No need to update a separate list since controllers hold the values
                                },
                              ),
                            ),
                            if (!isAddedBefore)
                              IconButton(
                                icon: const Icon(Icons.remove_circle,
                                    color: Colors.red),
                                onPressed: () {
                                  pickupControllers.value =
                                      List.from(pickupControllers.value)
                                        ..removeAt(index);
                                },
                              ),
                          ],
                        ),
                      );
                    }),
                    TextButton(
                      onPressed: () {
                        pickupControllers.value = [
                          ...pickupControllers.value,
                          TextEditingController(text: ''),
                        ];

                        WidgetsBinding.instance.addPostFrameCallback((_) {
                          focusNode.requestFocus();
                        });
                      },
                      child: Row(
                        children: [
                          const Icon(Icons.add).decorated(
                            border: Border.all(
                              color: ColorManager.primaryColor,
                            ),
                          ),
                          const SizedBox(width: 8),
                          Text(context.tr.addPickupPerson),
                        ],
                      ),
                    ),
                  ],
                ),
              ),
              onConfirm: () async {
                if (!formKey.value.currentState!.validate()) return;

                await addEditStudent();
              },
            ),
          );
        },
      );
    },
  );
}
