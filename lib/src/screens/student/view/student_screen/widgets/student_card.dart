import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/student/controllers/student_controller.dart';
import 'package:connectify_app/src/screens/student/view/student_details_screen/student_details_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/dialogs/base_delete_dialog.dart';
import 'package:connectify_app/src/shared/widgets/switch_button_widget/switch_button_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../../generated/assets.dart';
import '../../../../../shared/widgets/base_popupmenu/base_popupmenu.dart';
import '../../../../../shared/widgets/shared_widgets.dart';
import '../../../models/student_model.dart';

class StudentCard extends HookConsumerWidget {
  final StudentModel student;
  final bool isSignUp;
  final Widget navigateWidget;

  const StudentCard(
      {super.key,
      required this.student,
      this.isSignUp = false,
      required this.navigateWidget});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return isSignUp
        ? _SignupCard(student: student)
        : _StudentCard(
            student: student,
            navigateWidget: navigateWidget,
          );
  }
}

class _SignupCard extends StatelessWidget {
  final StudentModel student;

  const _SignupCard({required this.student});

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.topRight,
      children: [
        Container(
          height: 100.h,
          width: 100.w,
          padding: const EdgeInsets.all(AppSpaces.xSmallPadding),
          decoration: BoxDecoration(
              border: Border.all(color: ColorManager.black.withOpacity(0.4)),
              color: ColorManager.white,
              borderRadius:
                  BorderRadius.circular(AppRadius.baseContainerRadius),
              boxShadow: ConstantsWidgets.boxShadowFromBottom),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              ClipRRect(
                borderRadius:
                    BorderRadius.circular(AppRadius.baseContainerRadius),
                child: BaseCachedImage(
                  student.image?.url ?? '',
                  height: 50.h,
                  width: 53.w,
                  fit: BoxFit.cover,
                ),
              ),
              context.smallGap,
              Text(
                student.name ?? '',
                style: context.blueHint,
                maxLines: 2,
                textAlign: TextAlign.center,
              ),
              context.xSmallGap,
              Text(
                student.classModel?.name ?? '-',
                style: context.smallHint.copyWith(fontSize: 12),
                textAlign: TextAlign.center,
                maxLines: 1,
                overflow: TextOverflow.ellipsis,
              )
            ],
          ),
        ),
        Image.asset(Assets.iconsMale).paddingOnly(
          right: AppSpaces.smallPadding,
          top: AppSpaces.smallPadding,
        ),
      ],
    );
  }
}

class _StudentCard extends HookConsumerWidget {
  final StudentModel student;
  final Widget navigateWidget;

  const _StudentCard({required this.student, required this.navigateWidget});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final studentCtrl = ref.watch(studentChangeNotifierProvider(context));

    final isActive = useState<bool>(student.isActive);

    return InkWell(
      onTap: () {
        if (const UserModel().isTeacher) {
          context.to(StudentDetailsScreen(
            student: student,
          ));
        }
      },
      child: Stack(
        children: [
          Container(
            height: 100.h,
            width: 120.w,
            padding: const EdgeInsets.all(AppSpaces.xSmallPadding),
            decoration: BoxDecoration(
                border: Border.all(color: ColorManager.black.withOpacity(0.4)),
                color: student.isActive
                    ? ColorManager.white
                    : ColorManager.grey.withOpacity(0.5),
                borderRadius:
                    BorderRadius.circular(AppRadius.baseContainerRadius),
                boxShadow: ConstantsWidgets.boxShadowFromBottom),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.center,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                ClipRRect(
                  borderRadius:
                      BorderRadius.circular(AppRadius.baseContainerRadius),
                  child: BaseCachedImage(
                    student.image?.url ?? '',
                    height: 50.h,
                    width: 50.w,
                    fit: BoxFit.cover,
                    errorWidget: Image.asset(
                      Assets.imagesBaby,
                    ),
                  ),
                ),
                context.smallGap,
                Text(
                  student.name ?? '',
                  style: context.blueHint,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                  textAlign: TextAlign.center,
                ),
                Text(
                  student.classModel?.name ?? '-',
                  style: context.smallHint.copyWith(fontSize: 12),
                  textAlign: TextAlign.center,
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                )
              ],
            ),
          ),
          Image.asset(Assets.iconsMale).paddingOnly(
            left: AppSpaces.smallPadding,
            top: AppSpaces.mediumPadding,
          ),
          Positioned(
            right: -5.w,
            child: BasePopupmenu(
              // editOnTap: () => showAddStudentDialog(context,
              //     student: student, navigateWidget: navigateWidget),
              deleteOnTap: () => showDialog(
                  context: context,
                  builder: (context) => BaseDeleteDialog(
                      description: context.tr.areYouSureToDeleteThisStudent,
                      onConfirm: () async {
                        await studentCtrl.deleteStudent(
                            id: student.id!, navigateWidget: navigateWidget);
                      })),
              switchWidget: PopupMenuItem(
                  child: Row(
                children: [
                  Text(
                    context.tr.active,
                    style: context.labelMedium.copyWith(
                        fontWeight: FontWeight.bold,
                        color: isActive.value
                            ? ColorManager.buttonColor
                            : ColorManager.grey),
                  ),
                  const Spacer(),
                  StatefulBuilder(builder: (context, setState) {
                    return SwitchButtonWidget(
                      value: isActive,
                      onChanged: (value) {
                        isActive.value = value;

                        studentCtrl.activeOrDeActiveStudent(
                          id: student.id!,
                          isActive: value,
                        );

                        ref.refresh(getAllStudentsProvider(context));
                        ref.invalidate(getAllStudentsProvider(context));

                        setState(() {});
                      },
                    );
                  })
                ],
              )),
            ),
          ),
        ],
      ),
    );
  }
}
