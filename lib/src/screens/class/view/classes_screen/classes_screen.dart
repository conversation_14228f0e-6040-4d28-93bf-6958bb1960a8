import 'package:connectify_app/src/screens/class/controllers/class_controller.dart';
import 'package:connectify_app/src/screens/class/view/classes_screen/widgets/classes_list.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class ClassesScreen extends ConsumerWidget {
  const ClassesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final classController = ref.watch(getClassDataProvider(context));
    return WillPopScope(
      onWillPop: () async {
        context.toReplacement(const MainScreen());
        Log.f('classes back');
        return false;
      },
      child: Safe<PERSON><PERSON>(
        child: Scaffold(
          appBar: MainAppBar(title: context.tr.classes, isBackButton: true),
          backgroundColor: ColorManager.secondaryColor,
          body: Column(
            children: [
              Expanded(
                child: Column(
                  children: [
                    //! Text Setup Your Classes

                    context.largeGap,

                    //! Classes List
                    classController.get(data: (classData) {
                      return ClassesList(
                        classes: classData,
                      );
                    })
                  ],
                ).scroll(
                  padding: const EdgeInsets.all(AppSpaces.mediumPadding),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}
