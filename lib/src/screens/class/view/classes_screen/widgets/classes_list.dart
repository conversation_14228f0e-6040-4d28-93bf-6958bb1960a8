import 'package:connectify_app/src/screens/class/controllers/class_controller.dart';
import 'package:connectify_app/src/screens/class/view/classes_screen/widgets/class_card.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../models/class_model.dart';

class ClassesList extends ConsumerWidget {
  final bool isSignUp;
  final List<ClassModel> classes;

  const ClassesList(
      {super.key, this.isSignUp = false, this.classes = const []});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isEmptyList = classes.isEmpty;
    return isEmptyList ? Padding(
      padding: const EdgeInsets.only(top: 30.0),
      child: Center(
        child: Text(
          context.tr.noClasses,
          style: textTheme(context).headlineMedium,
        ),
      ),
    )  : ListView.builder(
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        itemBuilder: (context, index) =>
            ClassCard(classModel: classes[index], isSignUp: isSignUp),
        itemCount: classes.length);
  }
}
