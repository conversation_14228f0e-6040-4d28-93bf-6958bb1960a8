import 'package:connectify_app/src/screens/class/controllers/class_controller.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

class ClassCard extends ConsumerWidget {
  final bool isSignUp;
  final ClassModel classModel;

  const ClassCard({super.key, required this.classModel, this.isSignUp = false});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final classCtrl = ref.watch(classChangeNotifierController(context));
    final image = SizedBox(
      height: 50.h,
      width: 55.w,
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppRadius.baseContainerRadius),
        child: BaseCachedImage(
          classModel.image?.url ?? '',
          errorWidget: Image.network(
            'https://img.freepik.com/premium-vector/education-school-logo-design_586739-1327.jpg',
          ),
        ),
      ),
    );

    final titleAndSubtitle = Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          classModel.name,
          style: context.blueHint,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        context.xSmallGap,
        Text(
          classModel.description,
          style: context.smallHint.copyWith(fontSize: 12),
          maxLines: 2,
        ).sized(width: context.width * .45),
      ],
    );

    return isSignUp
        ? Container(
            padding: const EdgeInsets.all(AppSpaces.smallPadding),
            margin: const EdgeInsets.only(bottom: AppSpaces.mediumPadding),
            width: double.infinity,
            decoration: BoxDecoration(
                border: Border.all(color: ColorManager.grey),
                color: ColorManager.white,
                borderRadius:
                    BorderRadius.circular(AppRadius.baseContainerRadius),
                boxShadow: ConstantsWidgets.boxShadowFromBottom),
            child: Row(
              children: [
                //! Image
                image,

                context.smallGap,

                //! Title & Subtitle
                titleAndSubtitle,
              ],
            ).sized(width: context.width * .7),
          )
        : Container(
            padding: const EdgeInsets.all(AppSpaces.smallPadding),
            margin: const EdgeInsets.only(bottom: AppSpaces.mediumPadding),
            width: double.infinity,
            decoration: BoxDecoration(
                border: Border.all(color: ColorManager.grey),
                color: ColorManager.white,
                borderRadius:
                    BorderRadius.circular(AppRadius.baseContainerRadius),
                boxShadow: ConstantsWidgets.boxShadowFromBottom),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Row(
                  children: [
                    //! Image
                    image,

                    context.smallGap,

                    //! Title & Subtitle
                    titleAndSubtitle,
                  ],
                ),
                const Spacer(),
                Column(
                  children: [
                    Text(
                      context.tr.students,
                      style: context.smallHint.copyWith(
                          color: ColorManager.primaryColor,
                          fontWeight: FontWeight.bold),
                    ),
                    context.smallGap,
                    Text(
                      classModel.students
                              ?.where((element) => element.id != 0)
                              .length
                              .toString() ??
                          '0',
                      style: context.smallHint.copyWith(
                          color: ColorManager.primaryColor,
                          fontWeight: FontWeight.bold),
                    ),
                  ],
                ).paddingOnly(bottom: 5.h),
              ],
            ),
          );
  }
}
