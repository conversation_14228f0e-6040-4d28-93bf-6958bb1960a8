import 'package:connectify_app/src/shared/services/media/controller/media_controller.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';

class AddClassDialog extends HookConsumerWidget {
  final Widget navigateWidget;

  const AddClassDialog({super.key, required this.navigateWidget});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final mediaController = ref.watch(mediaPickerControllerProvider);

    return AddRectangleWidget(onTap: () {
      // showAddClassDialog(
      //   context,
      //   navigateWidget: navigateWidget,
      // ).then((value) {
      //   mediaController.clearFiles();
      // });
    });
  }
}
