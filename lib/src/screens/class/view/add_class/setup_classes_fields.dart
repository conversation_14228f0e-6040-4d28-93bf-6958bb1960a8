import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:xr_helper/xr_helper.dart';

class SetupClassesFields extends StatelessWidget {
  final Map<String, TextEditingController> controllers;

  const SetupClassesFields({
    super.key,
    required this.controllers,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        //! Name
        BaseTextField(
          controller: controllers[ApiStrings.name],
          title: context.tr.className,
          textInputType: TextInputType.text,
        ),

        context.largeGap,

        //! Description
        BaseTextField(
          controller: controllers[ApiStrings.description],
          title: context.tr.classDescription,
          textInputType: TextInputType.text,
          maxLines: 4,
          isRequired: false,
        ),
      ],
    );
  }
}
