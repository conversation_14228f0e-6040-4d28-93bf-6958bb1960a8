import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/teacher/controllers/teacher_controller.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../controllers/class_tab_bar_controller.dart';

class SelectedClassTabWidget extends ConsumerWidget {
  final ClassModel classModel;

  const SelectedClassTabWidget({super.key, required this.classModel});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tabCtrl = ref.watch(tabBarControllerProvider);
    final teacherCtrl =
        ref.watch(getTeacherByClassDataProvider((context, classModel.id!)));
    Log.i(classModel.students);

    return const SizedBox.shrink();
  }
}
