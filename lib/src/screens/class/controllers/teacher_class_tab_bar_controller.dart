import 'package:flutter_riverpod/flutter_riverpod.dart';

// * Bottom Nav Bar Controller ========================================
final tabBarController = Provider<TabBarController>(
  (ref) {
    return TabBarController();
  },
);

// * Bottom Nav Bar State Notifier ========================================
final tabBarControllerProvider = StateNotifierProvider<TabBarController, int>(
  (ref) => ref.watch(tabBarController),
);

class TabBarController extends StateNotifier<int> {
  TabBarController() : super(0);

  void changeIndex(int index) {
    state = index;
  }
}
