import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/class/repos/class_repo.dart';
import 'package:connectify_app/src/screens/class/view/classes_screen/classes_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../shared/data/remote/api_strings.dart';
import '../../../shared/services/media/controller/media_controller.dart';

// * Class Provider Controller ========================================
final classProviderController =
    Provider.family<ClassController, BuildContext>((ref, context) {
  final classRepo = ref.watch(classRepoProvider);

  return ClassController(context, classRepo: classRepo);
});

// * Change Notifier Class Controller ========================================
final classChangeNotifierController =
    ChangeNotifierProvider.family<ClassController, BuildContext>(
        (ref, context) {
  final classRepo = ref.watch(classRepoProvider);

  return ClassController(context, classRepo: classRepo);
});

// * Get Class Data ========================================
final getClassDataProvider =
    FutureProvider.family<List<ClassModel>, BuildContext>((ref, context) async {
  final classCtrl = ref.watch(classProviderController(context));

  return await classCtrl.getClasses();
});

// * Get Class Data ========================================
final getTeacherClassDataProvider =
    FutureProvider.family<List<ClassModel>, BuildContext>((ref, context) async {
  final classCtrl = ref.watch(classProviderController(context));

  return await classCtrl.getClasses();
});

//? ==========================================================================
class ClassController extends BaseVM {
  final BuildContext context;
  final ClassRepo classRepo;

  ClassController(this.context, {required this.classRepo});

  //? Get Classes ----------------------------------------------------
  Future<List<ClassModel>> getClasses() async {
    return await baseFunction(
      context,
      () async {
        final classes = await classRepo.getClasses();

        return classes;
      },
    );
  }

  //? Add Classes ----------------------------------------------------
  Future<void> addClass(
      {required Map<String, TextEditingController> controllers,
      required String pickedImage,
      required Widget navigateWidget}) async {
    return await baseFunction(context, () async {
      final classModel = ClassModel(
        name: controllers[ApiStrings.name]!.text,
        description: controllers[ApiStrings.description]!.text,
      );

      await classRepo.addClass(
        classModel: classModel,
        pickedImage: pickedImage,
      );

      if (context.mounted) {
        context.back();
        context.toReplacement(navigateWidget);
        context.showBarMessage(context.tr.addedSuccessfully);
      }
    }, additionalFunction: (_) => getClasses());
  }

  //? Edit Class Data ------------------------------------------
  Future<void> editClass(
      {required Map<String, TextEditingController> controllers,
      required int id,
      required String pickedImage,
      required Widget navigateWidget}) async {
    return await baseFunction(context, () async {
      final classModel = ClassModel(
        name: controllers[ApiStrings.name]!.text,
        description: controllers[ApiStrings.description]!.text,
      );

      await classRepo.editClass(
          id: id, classModel: classModel, pickedImage: pickedImage);

      if (context.mounted) {
        context.back();

        context.toReplacement(navigateWidget);
        context.showBarMessage(context.tr.editSuccessfully);
      }
    }, additionalFunction: (_) => getClasses());
  }

//? Delete Class Data ------------------------------------------
  Future<void> deleteClass({required int id}) async {
    return await baseFunction(context, () async {
      await classRepo.deleteClass(id: id);
      if (!context.mounted) return;
      context.back();
      context.toReplacement(const ClassesScreen());
      context.showBarMessage(context.tr.deletedSuccessfully, isError: true);
    }, additionalFunction: (_) => getClasses());
  }

  void clearData(
      {required Map<String, TextEditingController> controllers,
      required WidgetRef ref}) {
    controllers[ApiStrings.name]!.clear();
    controllers[ApiStrings.description]!.clear();
    ref.watch(mediaPickerControllerProvider).clearFiles();
  }
}
