import 'dart:developer';

import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

final authRepoProvider = Provider<AuthRepo>((ref) {
  final apiService = ref.watch(networkServiceProvider);
  return AuthRepo(apiService);
});

class AuthRepo with BaseRepository {
  final BaseApiServices _apiService;

  AuthRepo(this._apiService);

  final FirebaseAuth auth = FirebaseAuth.instance;

  int? _resendToken;
  String? _verificationID;

  String? get verificationID => _verificationID;

  Future<void> login({required String phone}) async {
    try {
      await auth.verifyPhoneNumber(
          phoneNumber: phone,
          forceResendingToken: _resendToken,
          verificationCompleted: _verificationCompleted,
          verificationFailed: (FirebaseAuthException e) {
            throw e;
          },
          codeSent: _codeSent,
          codeAutoRetrievalTimeout: _codeAutoRetrievalTimeout,
          timeout: const Duration(seconds: 120));
    } catch (e) {
      rethrow;
    }
  }

  //? Verification Completed ------------------------
  Future<void> _verificationCompleted(
      PhoneAuthCredential phoneAuthCredential) async {
    await auth.signInWithCredential(phoneAuthCredential).then((value) {
      log("You are logged in successfully");
    });
  }

  //? Code Sent ------------------------
  void _codeSent(String verificationId, int? resendToken) {
    _verificationID = verificationId;
    log('verificationId XXX :$verificationId');
    _resendToken = resendToken;
  }

  //? Code Auto Retrieval Timeout ------------------------
  void _codeAutoRetrievalTimeout(String verificationId) {
    _verificationID = verificationId;
  }

  Future<bool> verifyOTP(String otp) async {
    return await baseFunction(() async {
      log('sfsafsasaff $otp');
      PhoneAuthCredential credential = PhoneAuthProvider.credential(
          verificationId: _verificationID ?? '', smsCode: otp);

      try {
        final userData = await auth.signInWithCredential(credential);

        log('ggdgsg ${userData.user}  afdgagasgsag ${userData.user?.phoneNumber}');
        return userData.user != null;
      } on FirebaseAuthException catch (e, s) {
        if (e.code == 'invalid-verification-code') {
          Log.e('The verification code entered was invalid');
        }
        Log.e('Errorss: $e\n$s');
        return false;
      } catch (e, s) {
        Log.e('Error: $e\n$s');
        return false;
      }
    });
  }

  //! =======================================================
  //? Get User Data ------------------------
  // Future<UserModel> getUserData() async {
  //   final res = await _apiService.getResponse(ApiEndPoints.personalInfo);
  //
  //   final user = compute(userFromJson, res);
  //
  //   return user;
  // }

  Future<User?> getCurrentUser() async {
    return auth.currentUser;
  }

  //? Logout ------------------------
  Future<void> signOut() async {
    try {
      await auth.signOut();
    } catch (e) {
      rethrow;
    }
  }
}
