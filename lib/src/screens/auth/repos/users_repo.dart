import 'dart:developer';

import 'package:collection/collection.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

final userRepoProvider = Provider<UserRepo>((ref) {
  final netWorkApiServices = ref.watch(networkServiceProvider);

  return UserRepo(netWorkApiServices);
});

class UserRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  UserRepo(this._networkApiServices);

  Future<List<UserModel>> getUsersByPhone({
    required String phone,
  }) async {
    return baseFunction(() async {
      final response = await _networkApiServices
          .getResponse('${ApiEndpoints.usersPopulate}&filters[phone]=$phone');

      // log('asfasafsaf ${response[0][ApiStrings.nursery]}');
      return compute(responseToUserModelList, response);
    });
  }

  Future<void> addUser(
      {required UserModel user, required String pickedImage}) async {
    return baseFunction(() async {
      final userRes = await _networkApiServices.postResponse(
        ApiEndpoints.auth,
        body: user.toJson(),
        filePaths: [pickedImage],
        fromAuth: true,
      );

      final nursery = await _networkApiServices.postResponse(
        ApiEndpoints.nursery,
        body: {
          ApiStrings.admin: userRes[ApiStrings.user][ApiStrings.id],
          ApiStrings.name: user.name,
        },
        filePaths: [pickedImage],
        fieldName: ApiStrings.logo,
      );

      final res = await _networkApiServices.putResponse(
        '${ApiEndpoints.users}/${userRes['user']['id']}',
        data: {ApiStrings.nursery: nursery['id']},
        filePaths: [pickedImage],
        fromAuth: true,
      );

      // save student id to local
      await GetStorageService.setLocalData(
        key: LocalKeys.studentId,
        value: res[ApiStrings.user][ApiStrings.studentId],
      ); // save student id to local
      await GetStorageService.setLocalData(
        key: LocalKeys.classId,
        value: res[ApiStrings.user][ApiStrings.classId],
      );
      //
      // await GetStorageService.setLocalData(
      //     key: LocalKeys.nursery, value: nursery);
    });
  }

  Future<void> editUser(
      {required UserModel user,
      required String pickedImage,
      required int id}) async {
    return baseFunction(() async {
      final res = await _networkApiServices.putResponse(
        '${ApiEndpoints.users}/$id',
        data: user.toJson(isEdit: true),
        filePaths: [pickedImage],
        fromAuth: true,
      );

      // save student id to local
      // await GetStorageService.setLocalData(//fixme
      //   key: LocalKeys.studentId,
      //   value: res[ApiStrings.user][ApiStrings.studentId],
      // );
      // await GetStorageService.setLocalData(
      //   key: LocalKeys.classId,
      //   value: res[ApiStrings.user][ApiStrings.classId],
      // );

      if (pickedImage.isNotEmpty) {
        await _updateProfileImage(
            id: res[ApiStrings.user][ApiStrings.id].toString(),
            image: pickedImage);
      }
    });
  }

  Future<void> deleteUser({required int id}) async {
    return baseFunction(() async {
      await _networkApiServices.deleteResponse('${ApiEndpoints.users}/$id');
    });
  }

  Future<UserModel?> signedUser({required String phone}) async {
    return baseFunction(() async {
      final users = await getUsersByPhone(phone: phone);

      return users
          .firstWhereOrNull((element) => element.phone?.trim() == phone);
    });
  }

  Future<UserModel?> getUserById({required int id}) async {
    return baseFunction(() async {
      final response = await _networkApiServices.getResponse(
        ApiEndpoints.usersById(id),
      );

      log('asfasfasf ${ApiEndpoints.usersById(id)}\n FFFFF $response');

      return UserModel.fromJson(response);
    });
  }

  Future<Map<String, dynamic>?> getNurseryByUserId({required int? id}) async {
    return baseFunction(() async {
      final response =
          await _networkApiServices.getResponse(ApiEndpoints.nursery);
      return (response[ApiStrings.data] as List).firstWhereOrNull((element) =>
          element[ApiStrings.attributes][ApiStrings.admin][ApiStrings.data]
              [ApiStrings.id] ==
          id);
    });
  }

  Future<Map<String, dynamic>?> getNurseryById({required int? id}) async {
    return baseFunction(() async {
      final data =
          await _networkApiServices.getResponse(ApiEndpoints.nurseryById(id));

      final nursery = data[ApiStrings.data];

      return nursery;
    });
  }

  Future<void> _updateProfileImage(
      {required String? id, required String? image}) async {
    return baseFunction(() async {
      final uploadImageModel = UploadImageModel(
          refId: id.toString(),
          ref: 'plugin::users-permissions.user',
          field: 'image');
      await _networkApiServices.uploadFile(
          filePath: image!, uploadImageModel: uploadImageModel);
    });
  }

  Future<void> updateUser(
      {required UserModel user,
      String? filePath,
      bool changePassword = false}) async {
    return baseFunction(() async {
      await _networkApiServices.putResponse(
        '${ApiEndpoints.users}/${user.id}',
        data: changePassword ? user.toLoginJson() : user.toJson(),
        fromAuth: true,
        filePaths: [filePath ?? ''],
        fieldName: 'image',
      );
    });
  }

  Future<bool> login({required UserModel user}) async {
    return baseFunction(() async {
      final response = await _networkApiServices.postResponse(
          ApiEndpoints.login,
          body: user.toLoginJson(),
          fromAuth: true);

// nour el deen carpet
      //01129691263
      final studentId = response[ApiStrings.user][ApiStrings.studentId];
      final nurseryId = response[ApiStrings.user][ApiStrings.nurseryId];
      final classId = response[ApiStrings.user][ApiStrings.classId];
      final type = response[ApiStrings.user][ApiStrings.type];

      if (type != 'parent') {
        return false;
      }

      // save student id to local
      await GetStorageService.setLocalData(
          key: LocalKeys.studentId, value: studentId);

      // save student id to local
      await GetStorageService.setLocalData(
          key: LocalKeys.classId, value: classId);

      NotificationService.subscribeToTopic('parents$nurseryId');

      return response != null;
    });
  }

  Future<void> saveUserToLocal({required UserModel? user}) async {
    return baseFunction(() async {
      await GetStorageService.setLocalData(
          key: LocalKeys.user, value: user?.toJson());
    });
  }

  Future<UserModel?> getUserFromLocal() async {
    return baseFunction(() async {
      final user = await GetStorageService.getLocalData(key: LocalKeys.user);
      return user != null ? UserModel.fromJson(user) : null;
    });
  }
}
