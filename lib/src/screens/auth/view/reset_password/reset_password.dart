import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/auth/controllers/users_controller.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/auth/view/widgets/background_and_logo.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/icon_widget/fields_icon_widget.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class ResetPasswordScreen extends HookConsumerWidget {
  final UserModel user;

  const ResetPasswordScreen({super.key, required this.user});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isObscure = useState(true);
    //! -----------------------------------------------------------

    final formKey = useState(GlobalKey<FormState>()).value;

    //! -----------------------------------------------------------

    final newPasswordController = useTextEditingController();
    //! -----------------------------------------------------------

    final confirmPasswordController = useTextEditingController();
    //! -----------------------------------------------------------

    final userCtrl = ref.watch(userChangeNotifierProvider(context));
    //! -----------------------------------------------------------

    return BackgroundLogo(
        child: Form(
      key: formKey,
      child: Column(
        children: [
          context.largeGap,

          // * ================================================================
          Text(
            context.tr.resetPassword,
            style: context.boldTitle,
          ),
          context.xlLargeGap,

          // * ================================================================
          BaseTextField(
            enableToCopy: false,
            controller: newPasswordController,
            title: context.tr.enterNewPassword,
            isObscure: isObscure.value,
            textInputType: TextInputType.visiblePassword,
            validator: (value) {
              return Validations.password(value);
            },
            suffixIcon: InkWell(
              onTap: () {
                isObscure.value = !isObscure.value;
              },
              child: const FieldsIconWidget(
                icon: Assets.iconsCloseEye,
              ),
            ),
          ),

          context.fieldsGap,

          // * ================================================================
          BaseTextField(
            enableToCopy: false,
            controller: confirmPasswordController,
            title: context.tr.confirmNewPassword,
            isObscure: isObscure.value,
            textInputType: TextInputType.visiblePassword,
            validator: (value) {
              return Validations.password(value);
            },
            suffixIcon: InkWell(
              onTap: () {
                isObscure.value = !isObscure.value;
              },
              child: const FieldsIconWidget(
                icon: Assets.iconsCloseEye,
              ),
            ),
          ),
          context.xlLargeGap,

          // * ================================================================
          Button(
              isLoading: userCtrl.isLoading,
              loadingWidget: const LoadingWidget(),
              label: context.tr.save,
              onPressed: () async {
                final isNotMatchingPassword = newPasswordController.text !=
                    confirmPasswordController.text;
                if (!formKey.currentState!.validate()) return;
                if (isNotMatchingPassword) {
                  context.showBarMessage(context.tr.passwordsShouldMatch,
                      isError: true);
                  return;
                }

                final copiedUser =
                    user.copyWith(password: confirmPasswordController.text);

                await userCtrl.updateUser(user: copiedUser);
              })
        ],
      ).paddingAll(AppSpaces.mediumPadding),
    ));
  }
}
