import 'package:connectify_app/src/screens/auth/view/sign_in_screen/sign_in_screen.dart';
import 'package:connectify_app/src/screens/auth/view/widgets/background_and_logo.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

import '../sign_up_screen/sign_up_as/sign_up_as_screen.dart';

class WelcomeScreen extends StatelessWidget {
  const WelcomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return BackgroundLogo(
      child: Column(
        children: [
          Text(
            context.tr.speakWithConfidence,
            style: context.authTitle,
          ),
          context.smallGap,
          Text(
            context.tr.getTalkingFrom,
            style: context.greyLabelLarge,
            textAlign: TextAlign.center,
          ),
          context.xlLargeGap,
          context.xlLargeGap,
          context.xlLargeGap,

          //! Sign Up Button
          Button(
              label: context.tr.signUp,
              onPressed: () => context.to(const SignUpAsScreen())),
          context.mediumGap,
          //! Already have an account
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Text(
                context.tr.alreadyHaveAnAccount,
                style: context.subTitle,
              ),
              Align(
                alignment: Alignment.centerRight,
                child: TextButton(
                    onPressed: () {
                      context.to(const SignInScreen());
                    },
                    child: Text(
                      context.tr.signIn,
                      style: context.subTitle
                          .copyWith(color: ColorManager.primaryColor),
                    )),
              ),
            ],
          ),
        ],
      ).paddingAll(AppSpaces.mediumPadding),
    );
  }
}
