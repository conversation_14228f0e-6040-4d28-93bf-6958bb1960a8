import 'package:connectify_app/src/screens/auth/controllers/users_controller.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class LoginButton extends HookConsumerWidget {
  final Map<String, TextEditingController> controllers;
  final GlobalKey<FormState> formKey;

  const LoginButton(
      {super.key, required this.controllers, required this.formKey});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final userController = ref.watch(userChangeNotifierProvider(context));

    void login() {
      if (!formKey.currentState!.validate()) return;

      userController.login(
        controllers: controllers,
      );
    }

    return Button(
        isLoading: userController.isLoading,
        loadingWidget: const LoadingWidget(
          loadingType: LoadingType.button,
        ),
        label: context.tr.signIn,
        onPressed: login);
  }
}
