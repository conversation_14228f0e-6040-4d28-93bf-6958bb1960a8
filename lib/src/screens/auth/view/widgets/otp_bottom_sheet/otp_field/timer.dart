import 'package:circular_countdown_timer/circular_countdown_timer.dart';
import 'package:connectify_app/src/screens/auth/controllers/auth_controller.dart';
import 'package:connectify_app/src/screens/auth/view/widgets/otp_bottom_sheet/otp_field/main_otp_field_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

class TimerWidget extends ConsumerWidget {
  const TimerWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final auth = ref.watch(authControllerProvider(context));

    return CircularCountDownTimer(
      duration: 120,
      initialDuration: 0,
      controller: timerController,
      width: 40,
      height: 40,
      ringColor: Colors.transparent,
      fillColor: Colors.transparent,
      textStyle:
          const TextStyle(color: Colors.blue, fontWeight: FontWeight.bold),
      textFormat: CountdownTextFormat.MM_SS,
      isReverse: true,
      isReverseAnimation: true,
      isTimerTextShown: auth.disabledTimer,
      autoStart: false,
      onStart: () {
        auth.disabledTimer = true;
      },
      onComplete: () {
        auth.disabledTimer = false;
      },
    );
  }
}
