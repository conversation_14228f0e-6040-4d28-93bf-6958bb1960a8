import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import 'otp_field/main_otp_field_widget.dart';

class OtpBottomSheet extends StatelessWidget {
  final String? phoneNumber;
  final ValueNotifier<bool> phoneVerified;
  final Function? onSuccess;


  const OtpBottomSheet(
      {super.key, required this.phoneNumber, required this.phoneVerified,this.onSuccess});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: MediaQuery.of(context).viewInsets,
      margin: const EdgeInsets.all(20),
      decoration: const BoxDecoration(
        borderRadius: BorderRadius.only(
            topRight: Radius.circular(15), topLeft: Radius.circular(15)),
      ),
      child: SingleChildScrollView(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.start,
          mainAxisSize: MainAxisSize.min,
          children: [
            context.mediumGap,
            Text(
              context.tr.enterOtp,
              style: const TextStyle(
                fontSize: 20,
                color: ColorManager.primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            context.mediumGap,
            Consumer(
              builder: (context, ref, child) {
                return Text(
                  '${context.tr.sentVerificationCode} $phoneNumber',
                  style: const TextStyle(
                    fontSize: 14,
                  ),
                  textAlign: TextAlign.center,
                );
              },
            ),
            context.mediumGap,
            OtpFieldWidget(
              phone: phoneNumber!,
              phoneVerified: phoneVerified,
              onSuccess:onSuccess ,
            ),
          ],
        ),
      ),
    );
  }
}
