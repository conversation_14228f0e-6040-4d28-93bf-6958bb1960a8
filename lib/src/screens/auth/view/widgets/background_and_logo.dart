import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../generated/assets.dart';

class Background<PERSON>ogo extends StatelessWidget {
  final Widget child;
  final bool isSignIn;

  const BackgroundLogo({super.key, required this.child, this.isSignIn = false});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: SingleChildScrollView(
        child: isSignIn
            ? Column(
                children: [
                  Stack(
                    alignment: Alignment.bottomCenter,
                    children: [
                      const BgImage(),
                      Positioned(
                          bottom: 60, child: Image.asset(Assets.imagesLogo)),
                      Positioned(
                          bottom: 0,
                          child: Text(
                            context.tr.getTalkingFrom,
                            style: context.greyLabelLarge,
                            textAlign: TextAlign.center,
                          )),
                    ],
                  ),
                  context.xlLargeGap,
                  child
                ],
              ).scroll()
            : Column(
                children: [
                  const BgImage(),
                  Image.asset(Assets.imagesLogo),
                  child,
                ],
              ),
      ),
    );
  }
}
