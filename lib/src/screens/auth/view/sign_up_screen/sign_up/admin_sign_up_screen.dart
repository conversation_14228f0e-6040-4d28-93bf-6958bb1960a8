import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/auth/view/sign_up_screen/sign_up/widgets/app_bar.dart';
import 'package:connectify_app/src/screens/auth/view/sign_up_screen/sign_up/widgets/sign_up_button.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/sign_up_fields.dart';

class SignUpScreen extends HookWidget {
  final bool isAdmin;

  const SignUpScreen({super.key, this.isAdmin = false});

  @override
  Widget build(BuildContext context) {
    final isTermsChecked = useState(false);
    final phoneVerified = useState(true); //kDebugMode

    final _formKey = useState(GlobalKey<FormState>());

    final controllers = {
      ApiStrings.name: useTextEditingController(),
      ApiStrings.phone: useTextEditingController(
        text: kDebugMode ? '01140032722' : '',
        // text: kDebugMode ? '01118414599' : '',
      ),
      ApiStrings.email: useTextEditingController(),
      ApiStrings.password: useTextEditingController(),
    };

    return Scaffold(
      appBar: SignUpAppBar(
        isAdmin: isAdmin,
      ),
      body: Form(
        key: _formKey.value,
        child: ListView(
          children: [
            Image.asset(
              Assets.imagesLogo,
              height: 70,
              width: 70,
            ),

            context.largeGap,

            AdminSignUpFields(
              isAdmin: isAdmin,
              controllers: controllers,
              phoneVerified: phoneVerified,
            ),

            context.largeGap,

            _CheckBoxAndTerms(
              isTermsChecked: isTermsChecked,
            ),

            context.largeGap,

            //! Sign Up Button
            SignUpButton(
                controllers: controllers,
                valueNotifiers: (
                  isTermsChecked.value,
                  phoneVerified.value,
                  _formKey.value
                ),
                isAdmin: isAdmin),
          ],
        ).paddingAll(AppSpaces.mediumPadding),
      ),
    );
  }
}

class _CheckBoxAndTerms extends HookWidget {
  final ValueNotifier<bool> isTermsChecked;

  const _CheckBoxAndTerms({required this.isTermsChecked});

  @override
  Widget build(BuildContext context) {
    return CheckboxListTile(
      controlAffinity: ListTileControlAffinity.leading,
      contentPadding: EdgeInsets.zero,
      title: Row(
        children: [
          Text(
            context.tr.iHaveReadThe,
            style: context.authTitle,
          ),
          context.smallGap,
          TextButton(
            onPressed: () {
              launchUrl(Uri.parse('https://connectifyapp.org/Connectify.html'));
            },
            child: Text(
              context.tr.privacyPolicy,
              style:
                  context.boldTitle.copyWith(color: ColorManager.primaryColor),
            ),
          ),
        ],
      ),
      value: isTermsChecked.value,
      onChanged: (value) {
        isTermsChecked.value = value!;
      },
    );
  }
}
