part of 'user_model.dart';

enum UserTypeEnum { admin, teacher, child, parent }

UserTypeEnum _userTypeCheck(String? type) {
  switch (type) {
    case ApiStrings.admin:
      return UserTypeEnum.admin;

    case ApiStrings.teacher:
      return UserTypeEnum.teacher;

    default:
      return UserTypeEnum.parent;
  }
}

//? Get User Data from local

extension UserModelExtensions on UserModel {
  UserModel get currentUser {
    final user = GetStorageService.getLocalData(
      key: LocalKeys.user,
    );

    if (user == null) return UserModel.empty();

    return UserModel.fromJson(user);
  }

  bool get isTeacher => currentUser.userType == UserTypeEnum.teacher;

  bool get isAdmin => currentUser.userType == UserTypeEnum.admin;

  String get selectedUserStudentFilter {
    // if (isTeacher) {
    //   final classId = currentUser.classModel?.id;
    //   return '&filters[class][id]=$classId';
    // }

    return '';
  }
}
