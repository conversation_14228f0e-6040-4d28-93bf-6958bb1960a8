import 'dart:developer';

import 'package:connectify_app/src/screens/attendance/models/attendance_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:riverpod/riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

final attendanceRepoProvider = Provider<AttendanceRepo>((ref) {
  final networkApiService = ref.watch(networkServiceProvider);

  return AttendanceRepo(networkApiService);
});

class AttendanceRepo with BaseRepository {
  final BaseApiServices _networkApiService;

  AttendanceRepo(this._networkApiService);

  //? Get Attendance Data ------------------------------------------
  Future<List<AttendanceModel>> getAttendance() async {
    return baseFunction(
      () async {
        final response =
            await _networkApiService.getResponse(ApiEndpoints.attendances);

        final attendanceData = compute(responseToAttendanceModelList, response);

        return attendanceData;
      },
    );
  }

  //getAttendancesDataForToday
  Future<List<AttendanceModel>> getAttendanceByMonth({
    required String date,
  }) async {
    return baseFunction(
      () async {
        // final today = DateTime.now().formatDateToString;
        //2025-05-07
        final month = date.split('-')[1];

        log('asfasfafsa ${month}');

        final response = await _networkApiService.getResponse(
          '${ApiEndpoints.attendances}&filters[attendance_date][\$contains]=-$month-',

          // '${ApiEndpoints.attendances}&filters[attendance_date]=$today',
        );

        final attendanceData = compute(responseToAttendanceModelList, response);

        return attendanceData;
      },
    );
  }

  Future<List<AttendanceModel>> getAttendanceForToday() async {
    return baseFunction(
      () async {
        final today = DateTime.now().formatDateToString;

        final response = await _networkApiService.getResponse(
          '${ApiEndpoints.attendances}&filters[attendance_date]=$today',
        );

        final attendanceData = compute(responseToAttendanceModelList, response);

        return attendanceData;
      },
    );
  }
}
