import 'dart:developer';

import 'package:connectify_app/src/screens/attendance/models/attendance_model.dart';
import 'package:connectify_app/src/screens/attendance/repos/attendance_repo.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

// * Attendances Provider Controller ========================================
final attendancesProviderController =
    Provider.family<AttendancesController, BuildContext>((ref, context) {
  final attendanceRepo = ref.watch(attendanceRepoProvider);

  return AttendancesController(context, attendanceRepo: attendanceRepo);
});

final attendancesChangeNotifierController =
    ChangeNotifierProvider.family<AttendancesController, BuildContext>(
        (ref, context) {
  final attendanceRepo = ref.watch(attendanceRepoProvider);

  return AttendancesController(context, attendanceRepo: attendanceRepo);
});

// * Get Activities Data ========================================
final getAttendanceDataProvider =
    FutureProvider.family<List<AttendanceModel>, BuildContext>(
        (ref, context) async {
  final attendancesCtrl = ref.watch(attendancesProviderController(context));

  return await attendancesCtrl.getAttendancesData();
});

// * Get Activities Data For Month ========================================
final getAttendanceDataForMonthProvider = FutureProvider.family<
    List<AttendanceModel>,
    (BuildContext context, DateTime date)>((ref, params) async {
  final attendancesCtrl = ref.watch(attendancesProviderController(params.$1));

  return await attendancesCtrl.getAttendanceByMonth(
    date: params.$2.formatDateToString,
  );
});

// * Get Activities Data For Today ========================================
final getAttendanceDataForTodayProvider =
    FutureProvider.family<List<AttendanceModel>, BuildContext>(
        (ref, context) async {
  final attendancesCtrl = ref.watch(attendancesProviderController(context));

  return await attendancesCtrl.getAttendancesDataForToday();
});

//?==============================================================
class AttendancesController extends BaseVM {
  final BuildContext context;
  final AttendanceRepo attendanceRepo;

  AttendancesController(this.context, {required this.attendanceRepo});

  //? Get Activities Data ------------------------------------------
  Future<List<AttendanceModel>> getAttendancesData() async {
    return await baseFunction(
      context,
      () async {
        final attendances = await attendanceRepo.getAttendance();

        // Log.w('attendances =========== ${attendances.map((e) => e.toJson())}');

        return attendances;
      },
    );
  }

  //? Get Activities Data ------------------------------------------
  Future<List<AttendanceModel>> getAttendanceByMonth({
    required String date,
  }) async {
    return await baseFunction(
      context,
      () async {
        final attendances = await attendanceRepo.getAttendanceByMonth(
          date: date,
        );

        return attendances;
      },
    );
  }

  Future<List<AttendanceModel>> getAttendancesDataForToday() async {
    return await baseFunction(
      context,
      () async {
        final attendances = await attendanceRepo.getAttendanceForToday();

        final today = attendances.lastOrNull?.attendanceDate;

        log('sdlnvjkbvjs${today}');

        final totalAttendance = attendances
            .where((element) =>
                element.attendanceDate == DateTime.now().formatDateToString)
            .toList();

        log('qeffwirbviwdbjv${DateTime.now().formatDateToString}');

        Log.i(
            'totalAttendance ${totalAttendance.map((e) => e.attendanceDate)}');

        return totalAttendance;
      },
    );
  }

  void onNext(
      {required List attendanceDates,
      required ValueNotifier<int> selectedIndex}) {
    selectedIndex.value = (selectedIndex.value + 1) % attendanceDates.length;
  }

  void onPrev(
      {required List attendanceDates,
      required ValueNotifier<int> selectedIndex}) {
    selectedIndex.value = (selectedIndex.value - 1 + attendanceDates.length) %
        attendanceDates.length;
  }

  List<String> attendanceDates({required List<AttendanceModel> attendances}) =>
      attendances.map((e) => e.attendanceDate).toList();

  int lastAttendanceIndex({required List<AttendanceModel> attendances}) =>
      attendances.indexWhere((element) => attendances.last.id == element.id);
}
