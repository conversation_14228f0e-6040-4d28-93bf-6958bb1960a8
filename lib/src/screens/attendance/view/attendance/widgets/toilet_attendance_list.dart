import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/toilet/controller/toilet_controller.dart';
import 'package:connectify_app/src/screens/toilet/model/toilet_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class ToiletAttendanceList extends ConsumerWidget {
  final (BuildContext, DateTime) params;

  const ToiletAttendanceList({super.key, required this.params});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final toiletCtrl = ref.watch(getToiletByDayData(params));

    final toilets = toiletCtrl.when(
      data: (data) {
        return data;
      },
      error: (error, stackTrace) {
        return <ToiletModel>[];
      },
      loading: () {
        return <ToiletModel>[];
      },
    );

    return ListView.separated(
        itemCount: toilets.length,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        separatorBuilder: (context, index) => context.mediumGap,
        itemBuilder: (context, index) => BaseContainer(
                child: Row(
              children: [
                Image.asset(
                  Assets.imagesToilet,
                  width: 50,
                  height: 50,
                ),
                context.mediumGap,
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.tr.toilet,
                      style: context.title,
                    ),
                    Row(
                      children: [
                        Text(
                          '${toilets[index].toiletType?.name} -',
                          style: context.title,
                        ),
                        context.smallGap,
                        Text(
                          toilets[index].toiletWay?.name ?? '',
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            )));
  }
}
