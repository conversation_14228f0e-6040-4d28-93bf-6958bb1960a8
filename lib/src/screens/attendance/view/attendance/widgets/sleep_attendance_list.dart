import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/sleep/controller/sleep_controller.dart';
import 'package:connectify_app/src/screens/sleep/model/sleep_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class SleepAttendanceList extends ConsumerWidget {
  final (BuildContext, DateTime) params;

  const SleepAttendanceList({super.key, required this.params});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final sleepCtrl = ref.watch(getSleepByDayData(params));

    final sleepData = sleepCtrl.when(
      data: (data) {
        return data;
      },
      error: (error, stackTrace) {
        return <SleepModel>[];
      },
      loading: () {
        return <SleepModel>[];
      },
    );

    return ListView.separated(
        itemCount: sleepData.length,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        separatorBuilder: (context, index) => context.mediumGap,
        itemBuilder: (context, index) => BaseContainer(
                child: Row(
              children: [
                Image.asset(
                  Assets.imagesSleep,
                  width: 50,
                  height: 50,
                ),
                context.mediumGap,
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.tr.sleep,
                      style: context.title,
                    ),
                    Text(
                      '${sleepData[index].sleepStartTime} - ${sleepData[index].sleepEndTime}',
                      style: context.subTitle,
                    ),
                  ],
                ),
              ],
            )));
  }
}
