import 'package:connectify_app/src/screens/parent_activity/controllers/teacher_activities_controller.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class TeacherActivityAttendanceList extends ConsumerWidget {
  final (BuildContext, DateTime) params;

  const TeacherActivityAttendanceList({super.key, required this.params});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final teacherActivityCtrl =
        ref.watch(getTeacherActivityByDateProvider(params));

    return teacherActivityCtrl.get(
      data: (activities) {
        return ListView.separated(
            itemCount: activities.length,
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            separatorBuilder: (context, index) => context.mediumGap,
            itemBuilder: (context, index) => BaseContainer(
                    child: Row(
                  children: [
                    ClipRRect(
                      borderRadius: BorderRadius.circular(10),
                      child: Image.network(
                        activities[index].activity?.image?.url ??
                            AppConsts.activityPlaceholder,
                        width: 50,
                        height: 50,
                        fit: BoxFit.cover,
                      ),
                    ),
                    context.mediumGap,
                    Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          context.tr.activities,
                          style: context.title,
                        ),
                        Row(
                          children: [
                            SizedBox(
                              width: context.width * 0.35,
                              child: Text(
                                activities[index].activity?.name ?? '',
                                style: context.labelLarge,
                              ),
                            ),
                            context.smallGap,
                            SizedBox(
                              width: context.width * 0.35,
                              child: Text(
                                activities[index].description,
                                style: context.labelLarge,
                              ),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ],
                )));
      },
    );
  }
}
