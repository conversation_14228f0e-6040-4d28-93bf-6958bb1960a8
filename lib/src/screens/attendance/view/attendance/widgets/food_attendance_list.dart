import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/food/controller/food_controller.dart';
import 'package:connectify_app/src/screens/food/model/food_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class FoodAttendanceList extends ConsumerWidget {
  final (BuildContext, DateTime) params;

  const FoodAttendanceList({super.key, required this.params});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final foodCtrl = ref.watch(getFoodByDayData(params));

    final foods = foodCtrl.when(
      data: (data) {
        return data;
      },
      error: (error, stackTrace) {
        return <FoodModel>[];
      },
      loading: () {
        return <FoodModel>[];
      },
    );

    return ListView.separated(
        itemCount: foods.length,
        shrinkWrap: true,
        physics: const NeverScrollableScrollPhysics(),
        separatorBuilder: (context, index) => context.mediumGap,
        itemBuilder: (context, index) => BaseContainer(
                child: Row(
              children: [
                Image.asset(
                  Assets.imagesFood,
                  width: 50,
                  height: 50,
                ),
                context.mediumGap,
                Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      context.tr.food,
                      style: context.title,
                    ),
                    Row(
                      children: [
                        Text(
                          '${foods[index].mealType?.name} -' ?? '',
                          style: context.title,
                        ),
                        context.smallGap,
                        Text(
                          foods[index].mealAmount?.name ?? '',
                        ),
                      ],
                    ),
                  ],
                ),
              ],
            )));
  }
}
