import 'dart:developer';

import 'package:connectify_app/src/screens/attendance/controllers/attendance_controller.dart';
import 'package:connectify_app/src/screens/attendance/view/attendance/widgets/sleep_attendance_list.dart';
import 'package:connectify_app/src/screens/attendance/view/attendance/widgets/teacher_activity_attendance_list.dart';
import 'package:connectify_app/src/screens/attendance/view/attendance/widgets/toilet_attendance_list.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../models/attendance_model.dart';
import '../widgets/attendance_calendar_view.dart';
import 'widgets/food_attendance_list.dart';

class AttendanceScreen extends HookConsumerWidget {
  const AttendanceScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final selectedAttendanceDate = useState(DateTime.now());

    final params = (context, selectedAttendanceDate.value);

    log('asfafs ${selectedAttendanceDate.value}');

    final getAllAttendance =
        ref.watch(getAttendanceDataForMonthProvider(params));

    final attendances = getAllAttendance.when(
      data: (data) {
        return data;
      },
      error: (error, stackTrace) {
        return <AttendanceModel>[];
      },
      loading: () {
        return <AttendanceModel>[];
      },
    );

    return WillPopScope(
      onWillPop: () async {
        context.toReplacement(const MainScreen());
        return false;
      },
      child: SafeArea(
        child: Scaffold(
          appBar: MainAppBar(
            title: context.tr.attendance,
            isBackButton: true,
          ),
          body: Stack(
            children: [
              Column(
                children: [
                  AttendanceCalendarView(
                    attendances: attendances,
                    selectedDay: selectedAttendanceDate,
                  ),
                  context.mediumGap,
                  Expanded(
                      child: ListView(
                    padding: const EdgeInsets.all(AppSpaces.mediumPadding),
                    children: [
                      TeacherActivityAttendanceList(params: params),
                      context.mediumGap,
                      ToiletAttendanceList(params: params),
                      context.mediumGap,
                      FoodAttendanceList(params: params),
                      context.mediumGap,
                      SleepAttendanceList(params: params),
                    ],
                  ))
                ],
              ),
              if (getAllAttendance.isLoading) ...[
                const LoadingWidget(
                  loadingType: LoadingType.linear,
                )
              ]
            ],
          ),
        ),
      ),
    );
  }
}
