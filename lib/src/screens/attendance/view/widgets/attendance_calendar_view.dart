import 'package:connectify_app/src/screens/attendance/models/attendance_model.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:intl/intl.dart';
import 'package:table_calendar/table_calendar.dart';
import 'package:xr_helper/xr_helper.dart';

CalendarFormat _calendarFormat = CalendarFormat.month;

class AttendanceCalendarView extends HookConsumerWidget {
  final List<AttendanceModel> attendances;
  final ValueNotifier<DateTime> selectedDay;

  const AttendanceCalendarView({
    super.key,
    required this.attendances,
    required this.selectedDay,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _focusedDay = useState(DateTime.now());

    return StatefulBuilder(builder: (context, setState) {
      return TableCalendar(
        firstDay: DateTime.utc(2010, 10, 16),
        lastDay: DateTime.utc(2030, 3, 14),
        focusedDay: _focusedDay.value,
        calendarFormat: _calendarFormat,
        selectedDayPredicate: (day) {
          return isSameDay(selectedDay.value, day);
        },
        onDaySelected: (day, focusedDay) {
          if (!isSameDay(selectedDay.value, day)) {
            setState(() {
              selectedDay.value = day;
              _focusedDay.value = focusedDay;
            });
          }
        },
        calendarBuilders: CalendarBuilders(
          defaultBuilder: (
            context,
            date,
            _,
          ) {
            final attendanceOnDate = attendances.any((event) {
              final formattedDateTime = DateFormat('yyyy-MM-dd').format(date);
              return event.attendanceDate.contains(formattedDateTime);
            });

            return Container(
              height: 32.r,
              width: 32.r,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: attendanceOnDate ? Colors.green : Colors.red,
              ),
              child: Text(
                '${date.day}',
                style: context.labelLarge.copyWith(
                  color: Colors.white,
                ),
                // style: TextStyle().copyWith(fontSize: 16.0),
              ),
            );
          },
          selectedBuilder: (
            context,
            date,
            _,
          ) {
            return Container(
              height: 32.r,
              width: 32.r,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: Colors.blueGrey.shade400,
              ),
              child: Text(
                '${date.day}',
                style: context.labelLarge.copyWith(
                  color: Colors.white,
                ),
              ),
            );
          },
          todayBuilder: (
            context,
            date,
            _,
          ) {
            final attendanceOnDate = attendances.any((event) {
              final formattedDateTime = DateFormat('yyyy-MM-dd').format(date);
              return event.attendanceDate.contains(formattedDateTime);
            });

            return Container(
              height: 32.r,
              width: 32.r,
              alignment: Alignment.center,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                color: attendanceOnDate ? Colors.green : Colors.red,
              ),
              child: Text(
                '${date.day}',
                style: context.labelLarge.copyWith(
                  color: Colors.white,
                ),
              ),
            );
          },
        ),
        onFormatChanged: (format) {
          if (_calendarFormat != format) {
            setState(() {
              _calendarFormat = format;
            });
          }
        },
        onPageChanged: (focusedDay) {
          _focusedDay.value = focusedDay;

          selectedDay.value = focusedDay;
        },
        calendarStyle: CalendarStyle(
          isTodayHighlighted: true,
          selectedDecoration:
              AppConsts.eventBoxDecoration(ColorManager.primaryColor),
          markerSize: 4,
          defaultTextStyle: textTheme(context).labelLarge!,
          weekendTextStyle: textTheme(context).labelLarge!,
          todayDecoration: AppConsts.eventBoxDecoration(ColorManager.lightBlue),
          defaultDecoration: AppConsts.eventBoxDecoration(),
          weekendDecoration: AppConsts.eventBoxDecoration(),
          markerDecoration: AppConsts.markerBoxDecoration(),
          markerMargin: EdgeInsets.symmetric(
              horizontal: 1,
              vertical: _calendarFormat == CalendarFormat.month ? 5 : 4.5),
        ),
        eventLoader: (day) {
          return attendances.where((event) {
            // 2024-03-12
            final formatedDateTime = DateFormat('yyyy-MM-dd').format(day);

            return event.attendanceDate.contains(formatedDateTime);
            // return isSameDay(event.attendanceDate!, day);
          }).toList();
        },
      );
    });
  }
}
