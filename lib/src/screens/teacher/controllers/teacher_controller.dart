import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/screens/teacher/repos/teacher_repo.dart';
import 'package:connectify_app/src/screens/teacher/view/taechers_screen/teachers_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../shared/data/remote/api_strings.dart';

// * Teacher Provider Controller ==================================
final teacherProviderController =
    Provider.family<TeacherController, BuildContext>((ref, context) {
  final teacherRepo = ref.watch(teacherRepoProvider);

  return TeacherController(context, teacherRepo: teacherRepo);
});

// * Change Notifier Teacher Controller ==================================
final teacherChangeNotifierProvider =
    ChangeNotifierProvider.family<TeacherController, BuildContext>(
        (ref, context) {
  final teacherRepo = ref.watch(teacherRepoProvider);

  return TeacherController(context, teacherRepo: teacherRepo);
});

/// * Get Teacher Data ==========================================
final getTeacherDataProvider =
    FutureProvider.family<List<TeacherModel>, BuildContext>(
        (ref, context) async {
  final teacherController = ref.watch(teacherProviderController(context));

  return await teacherController.getTeachersData();
});

/// * Get Teacher By Class Data ==========================================
final getTeacherByClassDataProvider =
    FutureProvider.family<List<TeacherModel>, (BuildContext, int classId)>(
        (ref, params) async {
  final context = params.$1;
  final classId = params.$2;

  final teacherController = ref.watch(teacherProviderController(context));

  final filteredTeachers =
      await teacherController.getTeachersByClassData(classId: classId);

  return filteredTeachers;
});

//? =============================================================
class TeacherController extends BaseVM {
  final TeacherRepo teacherRepo;
  final BuildContext context;

  TeacherController(this.context, {required this.teacherRepo});

  //? Get Teachers ------------------------------------------------------------
  Future<List<TeacherModel>> getTeachersData() async {
    return await baseFunction(context, () {
      final teachers = teacherRepo.getTeachers();
      return teachers;
    });
  }

  //? Get Teachers By Class ------------------------------------------------------------
  Future<List<TeacherModel>> getTeachersByClassData(
      {required int classId}) async {
    return await baseFunction(context, () {
      final teachers = teacherRepo.getTeachersByClass(classId: classId);
      return teachers;
    });
  }

  //? Add Teacher
// ? ------------------------------------------------------------
  Future<void> addTeacher(
      {required Map<String, TextEditingController> controllers,
      required ClassModel? selectedClass,
      required Widget navigateWidget,
      required String pickedImage}) async {
    return await baseFunction(context, () async {
      final teacher = TeacherModel(
        name: controllers[ApiStrings.name]!.text,
        description: controllers[ApiStrings.jobTitle]!.text,
        phone: controllers[ApiStrings.phone]!.text,
        // classModel: selectedClass,
      );

      await teacherRepo.addTeacher(
        teacher: teacher,
        pickedImage: pickedImage,
      );
      if (context.mounted) {
        context.back();
        context.toReplacement(navigateWidget);
        context.showBarMessage(context.tr.addedSuccessfully);
      }
    }, additionalFunction: (_) => getTeachersData());
  }

  //? Edit Teachers Data ----------------------------------------------------
  Future<void> editTeacher({
    required Map<String, TextEditingController> controllers,
    required ClassModel? selectedClass,
    required int id,
    required String pickedImage,
    required Widget navigateWidget,
  }) async {
    return await baseFunction(context, () async {
      final teacher = TeacherModel(
        name: controllers[ApiStrings.name]!.text,
        description: controllers[ApiStrings.jobTitle]!.text,
        phone: controllers[ApiStrings.phone]!.text,
        // classModel: selectedClass,
      );

      await teacherRepo.editTeacher(
        teacher: teacher,
        pickedImage: pickedImage,
        id: id,
      );

      if (context.mounted) {
        context.back();
        context.toReplacement(navigateWidget);
        context.showBarMessage(context.tr.editSuccessfully);
      }
    }, additionalFunction: (_) => getTeachersData());
  }

  //? Delete Teacher ===============================
  Future<void> deleteTeacher({
    required int id,
    required Widget navigateWidget,
  }) async {
    return await baseFunction(context, () async {
      await teacherRepo.deleteTeacher(id: id);
      if (context.mounted) {
        context.back();
        context.toReplacement(navigateWidget);
        context.showBarMessage(context.tr.deletedSuccessfully, isError: true);
      }
    });
  }

  //? Active or DeActive Student Data ------------------------------------------
  Future<void> activeOrDeActiveStudent(
      {required int id, required bool isActive}) async {
    return await baseFunction(context, () async {
      await teacherRepo.activeDeActiveTeacher(id: id, isActive: isActive);
      if (!context.mounted) return;

      context.toReplacement(const TeachersScreen());

      context.showBarMessage(context.tr.editSuccessfully);
    });
  }

  Future<void> updateTeacher({
    required TeacherModel teacher,
  }) async {
    return await baseFunction(context, () async {
      await teacherRepo.updateUser(teacher: teacher);
    });
  }
}
