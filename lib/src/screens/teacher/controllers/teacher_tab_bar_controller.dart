import 'package:flutter_riverpod/flutter_riverpod.dart';

// * Bottom Nav Bar Controller ========================================
final teacherTabBarController = Provider<TeacherTabBarController>(
  (ref) {
    return TeacherTabBarController();
  },
);

// * Bottom Nav Bar State Notifier ========================================
final teacherTabBarControllerProvider = StateNotifierProvider<TeacherTabBarController, int>(
  (ref) => ref.watch(teacherTabBarController),
);

class TeacherTabBarController extends StateNotifier<int> {
  TeacherTabBarController() : super(0);

  void changeIndex(int index) {
    state = index;
  }
}
