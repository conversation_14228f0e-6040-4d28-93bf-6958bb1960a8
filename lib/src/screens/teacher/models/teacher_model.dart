import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/shared/consts/app_constants.dart';

import '../../../shared/data/remote/api_strings.dart';
import '../../../shared/shared_models/base_media_model.dart';

List<TeacherModel> responseToTeacherModelList(response) {
  final data = (response as List?) ?? [];

  final teachers = data.map((e) => TeacherModel.fromJson(e)).toList();

  return teachers;
}

class TeacherModel extends UserModel {
  final List<ClassModel?> teacherClasses;
  final bool isActive;

  const TeacherModel({
    super.id,
    super.name,
    super.description,
    super.image,
    super.phone,
    super.fcmToken,
    this.teacherClasses = const [],
    this.isActive = true,
  });

  factory TeacherModel.fromJson(Map<String, dynamic> json) {
    final image = json[ApiStrings.image] != null
        ? BaseMediaModel.fromJson(json[ApiStrings.image])
        : null;

    // log('asfasfsfsafa ${json[ApiStrings.classes]}');

    return TeacherModel(
      id: json[ApiStrings.id],
      name: json[ApiStrings.username] ?? '',
      fcmToken: json[ApiStrings.fcmToken] ?? '',
      description: json[ApiStrings.jobTitle] ?? '',
      phone: json[ApiStrings.phone] ?? '',
      isActive: json[ApiStrings.isActive] ?? true,
      teacherClasses: json[ApiStrings.teacherClasses] != null
          ? (json[ApiStrings.teacherClasses] as List)
              .map<ClassModel>((e) => ClassModel.fromJsonWithOutAttributes(e))
              .toList()
          : [],
      image: image,
    );
  }

  factory TeacherModel.fromAttributesJson(Map<String, dynamic> json) {
    final attributes = json[ApiStrings.attributes];

    return TeacherModel(
      id: json[ApiStrings.id],
      fcmToken: json[ApiStrings.fcmToken],
      name:
          attributes[ApiStrings.username] ?? attributes[ApiStrings.name] ?? '',
      description: attributes[ApiStrings.jobTitle] ?? '',
    );
  }

  //? Copy With
  @override
  TeacherModel copyWith(
      {int? id,
      String? name,
      String? description,
      BaseMediaModel? image,
      String? phone,
      List<ClassModel?>? teacherClasses,
      List<ClassModel>? classes,
      UserTypeEnum? userType,
      String? email,
      String? password,
      String? fcmToken}) {
    return TeacherModel(
      id: id ?? this.id,
      teacherClasses: teacherClasses ?? this.teacherClasses,
      name: name ?? this.name,
      description: description ?? this.description,
      image: image ?? this.image,
      phone: phone ?? this.phone,
      fcmToken: fcmToken ?? this.fcmToken,
    );
  }

  @override
  Map<String, dynamic> toJson(
      {bool isEdit = false,
      //? For Parents
      int? studentId,
      int? classId}) {
    return {
      if (id != null) ApiStrings.id: id,
      ApiStrings.username: name,
      ApiStrings.jobTitle: description,
      ApiStrings.phone: phone,
      ApiStrings.type: ApiStrings.teacher,
      if (teacherClasses.isNotEmpty)
        ApiStrings.teacherClasses: teacherClasses.map((e) => e?.id).toList(),
      // if (classModel != null)
      // ApiStrings.classString: teacherClasses.firstOrNull?.id,
      // if (classModel?.id != null) ApiStrings.classString: classModel?.id,
      if (!isEdit) ApiStrings.email: '$<EMAIL>',
      if (!isEdit) ApiStrings.password: '${name.trim()}@1234',
    };
  }

  Map<String, dynamic> toAssignedClassJson({bool unAssign = false}) {
    return {
      ApiStrings.classString:
          unAssign ? AppConsts.class0ID : classes?.map((e) => e.id).toList(),
    };
  }

  @override
  List<Object?> get props => [
        id,
        name,
        description,
        image,
        phone,
        fcmToken,
        teacherClasses,
        classes,
      ];
}

// import 'package:connectify_app/src/screens/auth/models/user_model.dart';
// import 'package:connectify_app/src/screens/class/models/class_model.dart';
//
// import '../../../shared/data/remote/api_strings.dart';
// import '../../../shared/shared_models/base_media_model.dart';
//
// List<TeacherModel> responseToTeacherModelList(response) {
//   final data = (response as List?) ?? [];
//
//   final teachers = data.map((e) => TeacherModel.fromJson(e)).toList();
//
//   return teachers;
// }
//
// class TeacherModel extends UserModel {
//   final ClassModel? classModel;
//   final bool isActive;
//
//   const TeacherModel({
//     super.id,
//     super.name,
//     super.description,
//     super.image,
//     super.phone,
//     this.classModel,
//     this.isActive = true,
//   });
//
//   factory TeacherModel.fromJson(Map<String, dynamic> json) {
//     final image = json[ApiStrings.image] != null
//         ? BaseMediaModel.fromJson(json[ApiStrings.image])
//         : null;
//
//     final classModel = json[ApiStrings.classString] != null
//         ? ClassModel.fromJsonWithOutAttributes(json[ApiStrings.classString])
//         : null;
//
//     return TeacherModel(
//       id: json[ApiStrings.id],
//       name: json[ApiStrings.username] ?? '',
//       description: json[ApiStrings.jobTitle] ?? '',
//       phone: json[ApiStrings.phone] ?? '',
//       isActive: json[ApiStrings.isActive] ?? true,
//       classModel: classModel,
//       image: image,
//     );
//   }
//
//   factory TeacherModel.fromAttributesJson(Map<String, dynamic> json) {
//     final attributes = json[ApiStrings.attributes];
//
//     // final image = attributes[ApiStrings.photo] != null
//     //     ? BaseMediaModel.fromJson(attributes[ApiStrings.photo])
//     //     : null;
//     //
//     // final classModel = attributes[ApiStrings.classString] != null
//     //     ? ClassModel.fromJsonWithOutAttributes(
//     //         attributes[ApiStrings.classString])
//     //     : null;
//
//     return TeacherModel(
//       id: json[ApiStrings.id],
//       name:
//           attributes[ApiStrings.username] ?? attributes[ApiStrings.name] ?? '',
//       description: attributes[ApiStrings.jobTitle] ?? '',
//       // classModel: classModel,
//       // image: image,
//     );
//   }
//
//   //? Copy With
//   @override
//   TeacherModel copyWith({
//     // int? id,
//     // ClassModel? classModel,
//     // String? name,
//     // String? description,
//     // BaseMediaModel? image,
//     // String? phone,
//     int? id,
//     String? name,
//     String? description,
//     BaseMediaModel? image,
//     String? phone,
//     ClassModel? classModel,
//     UserTypeEnum? userType,
//     String? email,
//     String? password,
//   }) {
//     return TeacherModel(
//       id: id ?? this.id,
//       classModel: classModel ?? this.classModel,
//       name: name ?? this.name,
//       description: description ?? this.description,
//       image: image ?? this.image,
//       phone: phone ?? this.phone,
//     );
//   }
//
//   @override
//   Map<String, dynamic> toJson({bool isEdit = false}) {
//     return {
//       if (id != null) ApiStrings.id: id,
//       ApiStrings.username: name,
//       ApiStrings.jobTitle: description,
//       ApiStrings.phone: phone,
//       if (classModel?.id != null) ApiStrings.classString: classModel?.id,
//       if (!isEdit) ApiStrings.type: ApiStrings.teacher,
//       if (classModel != null) ApiStrings.classString: classModel!.id,
//       if (!isEdit) ApiStrings.email: '$<EMAIL>',
//       if (!isEdit) ApiStrings.password: '${name.trim()}@1234',
//     };
//   }
//
//   Map<String, dynamic> toAssignedClassJson() {
//     return {
//       ApiStrings.classString: classModel?.id,
//     };
//   }
// }
