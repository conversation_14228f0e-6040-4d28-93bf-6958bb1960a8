import 'dart:developer';

import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

final teacherRepoProvider = Provider<TeacherRepo>((ref) {
  final netWorkApiServices = ref.watch(networkServiceProvider);

  return TeacherRepo(netWorkApiServices);
});

//? ========================================================================
class TeacherRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  TeacherRepo(this._networkApiServices);

  //? Get Teachers Data ----------------------------------------------------
  Future<List<TeacherModel>> getTeachers() async {
    return baseFunction(() async {
      final response =
          await _networkApiServices.getResponse(ApiEndpoints.teachers);

      final teacherData = compute(responseToTeacherModelList, response);

      return teacherData;
    });
  }

  //? Get Teachers By Class Data ----------------------------------------------------
  Future<List<TeacherModel>> getTeachersByClass({required int classId}) async {
    return baseFunction(() async {
      final response = await _networkApiServices
          .getResponse(ApiEndpoints.teacherByClass(classId));

      final teacherData = compute(responseToTeacherModelList, response);

      return teacherData;
    });
  }

  //? Add Teachers Data ----------------------------------------------------
  Future<void> addTeacher(
      {required TeacherModel teacher, required String pickedImage}) async {
    return await baseFunction(() async {
      final res = await _networkApiServices.postResponse(
        ApiEndpoints.auth,
        body: teacher.toJson(),
        filePaths: [pickedImage],
        fromAuth: true,
      );

      try {
        await _updateProfileImage(
          id: res[ApiStrings.user][ApiStrings.id].toString(),
          image: pickedImage,
        );
      } catch (e) {}
    });
  }

  //? Edit Teachers Data ----------------------------------------------------
  Future<void> editTeacher(
      {required TeacherModel teacher,
      required String pickedImage,
      required int id}) async {
    return await baseFunction(() async {
      final res = await _networkApiServices.putResponse(
        '${ApiEndpoints.users}/$id',
        data: teacher.toJson(isEdit: true),
        // filePaths: [pickedImage],
        fromAuth: true,
      );

      try {
        await _updateProfileImage(
          id: res[ApiStrings.id].toString(),
          image: pickedImage,
        );
      } catch (e) {}
    });
  }

  Future<void> deleteTeacher({required int id}) async {
    return await baseFunction(() async {
      await _networkApiServices.deleteResponse('${ApiEndpoints.users}/$id');
    });
  }

  //? Active or DeActive Student Data ------------------------------------------
  Future<void> activeDeActiveTeacher(
      {required int id, required bool isActive}) async {
    return await baseFunction(() async {
      await _networkApiServices.putResponse('${ApiEndpoints.users}/$id',
          data: {
            ApiStrings.isActive: isActive,
          },
          fromAuth: true);
    });
  }

  Future<void> _updateProfileImage({
    required String? id,
    required String? image,
  }) async {
    try {
      log('🟢id 🟢 $id');

      final uploadImageModel = UploadImageModel(
        refId: id.toString(),
        ref: 'plugin::users-permissions.user',
        field: 'image',
      );

      final response = await _networkApiServices.uploadFile(
        filePath: image ?? '',
        uploadImageModel: uploadImageModel,
      );

      log('🟢addProfileImage🟢 $response');
    } catch (error) {
      log('🔴addProfileImageError🔴 $error');
      rethrow;
    }
  }

  Future<void> updateUser({
    required TeacherModel teacher,
  }) async {
    return await baseFunction(() async {
      await _networkApiServices.putResponse(
          '${ApiEndpoints.users}/${teacher.id}',
          data: teacher.toAssignedClassJson(),
          fromAuth: true);
    });
  }
}
