import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../home/<USER>/main_screen.dart';
import '../../../home/<USER>/main_screen/widgets/main_app_bar.dart';
import '../../controllers/teacher_controller.dart';

class TeachersScreen extends ConsumerWidget {
  const TeachersScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final teacherController = ref.watch(getTeacherDataProvider(context));

    return WillPopScope(
      onWillPop: () async {
        context.toReplacement(const MainScreen());
        return false;
      },
      child: <PERSON><PERSON><PERSON>(
        child: Scaffold(
          appBar: MainAppBar(title: context.tr.staff, isBackButton: true),
          body: SizedBox(),
        ),
      ),
    );
  }
}
