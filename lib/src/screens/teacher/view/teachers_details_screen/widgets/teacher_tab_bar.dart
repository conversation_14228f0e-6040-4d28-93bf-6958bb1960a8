import 'package:connectify_app/src/screens/teacher/controllers/teacher_tab_bar_controller.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../class/controllers/class_tab_bar_controller.dart';

class TeacherTabBar extends ConsumerWidget {
  final List<String> tabs;
  final int initialIndex;
  final Function(int)? onTab;
  final TeacherModel teacherModel;

  const TeacherTabBar({
    super.key,
    required this.tabs,
    required this.initialIndex,
    required this.onTab,
    required this.teacherModel,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final tabCtrlProvider = ref.watch(teacherTabBarControllerProvider);

    return Column(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        DefaultTabController(
          initialIndex: initialIndex,
          length: tabs.length,
          child: TabBar(
            splashBorderRadius: BorderRadius.circular(AppRadius.tabBarRadius),
            tabAlignment: TabAlignment.start,
            unselectedLabelStyle: context.labelLarge,
            onTap: onTab,
            indicatorColor: Colors.transparent,
            indicatorSize: TabBarIndicatorSize.tab,
            dividerColor: Colors.transparent,
            unselectedLabelColor: context.isDark
                ? ColorManager.grey.withOpacity(0.5)
                : ColorManager.secondaryColor,
            labelPadding: const EdgeInsets.all(8),
            labelColor: ColorManager.white,
            labelStyle: context.subTitle.copyWith(fontWeight: FontWeight.w400),
            isScrollable: true,
            tabs: tabs.indexed.map((e) {
              final index = e.$1;

              if (index == tabCtrlProvider) {
                return Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
                  decoration: BoxDecoration(
                    color: ColorManager.primaryColor,
                    borderRadius: BorderRadius.circular(AppRadius.tabBarRadius),
                  ),
                  child: Text(e.$2),
                );
              }
              return Text(
                e.$2,
                style: context.subTitle.copyWith(fontWeight: FontWeight.w200),
              );
            }).toList(),
          ),
        ),
      ],
    );
  }
}
