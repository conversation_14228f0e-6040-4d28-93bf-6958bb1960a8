import 'package:connectify_app/src/screens/class/models/class_model.dart';
import 'package:connectify_app/src/screens/class/view/classes_screen/widgets/class_card.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../shared/widgets/shared_widgets.dart';
import '../../../../models/teacher_model.dart';
import 'assign_to_class_button.dart';

class TeacherInfoTab extends StatelessWidget {
  final TeacherModel teacher;

  const TeacherInfoTab({super.key, required this.teacher});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '${context.tr.phoneNumber} : ${teacher.phone}',
          style: context.title,
          textAlign: TextAlign.start,
        ),
        context.mediumGap,
        AddRectangleWidget(
          title: context.tr.assignToClass,
          onTap: () {
            showDialog(
              context: context,
              builder: (context) {
                return AssignToClassButton(
                  teacher: teacher,
                );
              },
            );
          },
        ),
        context.largeGap,
        ClassCard(
          classModel: teacher.teacherClasses.firstOrNull ?? ClassModel.empty(),
          isSignUp: true,
        ),
      ],
    );
  }
}
