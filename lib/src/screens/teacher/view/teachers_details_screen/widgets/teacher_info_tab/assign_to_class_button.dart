import 'dart:developer';

import 'package:connectify_app/src/screens/class/controllers/class_controller.dart';
import 'package:connectify_app/src/screens/teacher/models/teacher_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';
import 'assign_to_class_card.dart';

class AssignToClassButton extends ConsumerWidget {
  final TeacherModel teacher;
  const AssignToClassButton({super.key,required this.teacher});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final classCtrl = ref.watch(getClassDataProvider(context));

    return Dialog(
        surfaceTintColor: ColorManager.white,
        insetPadding: const EdgeInsets.all(AppSpaces.mediumPadding),
        child: classCtrl.get(
          data: (classes) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Text(
                context.tr.assignToClass,
                style: context.title,
              ),
              context.largeGap,
              ListView.separated(
                  shrinkWrap: true,
                  itemCount: classes.length,
                  separatorBuilder: (context, index) => context.mediumGap,
                  itemBuilder: (context, index) {
                  final teacherIds =  classes[index].teachers!.map((e) => e.id).toList();
                  final isTeacherFound = teacherIds.contains(teacher.id);

                    return
                      AssignClassCard(classModel: classes[index], isTeacherFound: isTeacherFound, teacher: teacher,);
                  }),
            ],
          ).paddingSymmetric(vertical: AppSpaces.largePadding),
        ));
  }
}
