import 'package:connectify_app/src/screens/auth/controllers/auth_controller.dart';
import 'package:connectify_app/src/screens/home/<USER>/bottom_nav_controller.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/main_app_bar.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/services/app_settings/controller/settings_controller.dart';
import 'package:connectify_app/src/shared/widgets/dialogs/base_delete_dialog.dart';
import 'package:connectify_app/src/shared/widgets/dialogs/language_dialog.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

import 'widgets/profile_dialog/profile_dialog.dart';

class SettingsScreen extends ConsumerWidget {
  const SettingsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerProvider(context));
    final bottomNavCtrl = ref.read(bottomNavController);
    final settingsController = ref.watch(settingsControllerProvider);

    return Scaffold(
      appBar: MainAppBar(
        title: context.tr.settings,
        isBackButton: true,
      ),
      body: Center(
        child: Column(
          children: [
            //! Profile List Tile ========================================
            ListTile(
              onTap: () {
                showDialog(
                  context: context,
                  builder: (context) {
                    return const ProfileDialog();
                  },
                );
              },
              title: Text(
                context.tr.profile,
                style: context.title.copyWith(fontWeight: FontWeight.bold),
              ),
              leading: const Icon(
                Icons.person,
                color: ColorManager.primaryColor,
              ),
              trailing: const Icon(
                Icons.arrow_forward_ios,
                color: ColorManager.grey,
              ),
            ),

            context.largeGap,

            //! Change Language List Tile ========================================
            ListTile(
              onTap: () {
                showDialog(
                  context: context,
                  builder: (context) {
                    return const LanguageDialog();
                  },
                );
              },
              title: Text(
                context.tr.changeLanguage,
                style: context.title.copyWith(fontWeight: FontWeight.bold),
              ),
              leading: const Icon(
                Icons.language,
                color: ColorManager.primaryColor,
              ),
              trailing: Text(
                settingsController.locale.languageCode == 'en'
                    ? context.tr.english
                    : context.tr.arabic,
                style: context.subTitle.copyWith(
                  color: ColorManager.primaryColor,
                ),
              ),
            ),

            context.largeGap,

            //! Change Password List Tile ========================================
            ListTile(
              onTap: () {
                showDialog(
                  context: context,
                  builder: (context) {
                    return const ProfileDialog(
                      changePassword: true,
                    );
                  },
                );
              },
              title: Text(
                context.tr.changePassword,
                style: context.title.copyWith(fontWeight: FontWeight.bold),
              ),
              leading: const Icon(
                Icons.lock,
                color: ColorManager.primaryColor,
              ),
              trailing: const Icon(
                Icons.arrow_forward_ios,
                color: ColorManager.grey,
              ),
            ),

            context.largeGap,

            //! Delete Account ========================================
            ListTile(
              onTap: () {
                showDialog(
                    context: context,
                    builder: (context) => BaseDeleteDialog(
                        description: context.tr.areYouSureToDeleteYourAccount,
                        onConfirm: () async {
                          await authController.updatePassword(controllers: {
                            'password': TextEditingController(text: '000000'),
                          });
                          await authController.logout();
                        }));
              },
              title: Text(
                context.tr.deleteAccount,
                style: context.title.copyWith(fontWeight: FontWeight.bold),
              ),
              leading: const Icon(
                Icons.delete,
                color: ColorManager.errorColor,
              ),
              trailing: const Icon(
                Icons.arrow_forward_ios,
                color: ColorManager.grey,
              ),
            ),

            const Spacer(),

            //! Logout Button ========================================
            Button(
              label: context.tr.logout,
              color: ColorManager.primaryColor,
              onPressed: () async {
                await authController.logout();
                bottomNavCtrl.changeIndex(0);
              },
            ).sized(
              height: 40.h,
              width: context.width * .9,
            ),

            context.largeGap,
          ],
        ),
      ),
    );
  }
}
