import 'package:connectify_app/src/screens/auth/controllers/auth_controller.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/services/media/controller/media_controller.dart';
import 'package:connectify_app/src/shared/widgets/loading/loading_widget.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import 'profile_fields.dart';

class ProfileDialog extends HookConsumerWidget {
  final bool changePassword;

  const ProfileDialog({
    super.key,
    this.changePassword = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final _formKey = useState(GlobalKey<FormState>());

    final currentUser = const UserModel().currentUser;

    final controllers = {
      ApiStrings.name: useTextEditingController(text: currentUser.name),
      ApiStrings.phone: useTextEditingController(
        text: currentUser.phone,
      ),
      ApiStrings.email: useTextEditingController(
        text: currentUser.email,
      ),
      ApiStrings.password: useTextEditingController(),
      ApiStrings.passwordConfirmation: useTextEditingController(),
    };

    final phoneVerified = useState(false);

    final mediaController = ref.watch(mediaPickerControllerProvider);

    final authController = ref.watch(authControllerProvider(context));

    return WillPopScope(
      onWillPop: () async {
        mediaController.clearFiles();

        return true;
      },
      child: BaseDialog(
        child: Form(
          key: _formKey.value,
          child: Padding(
            padding: const EdgeInsets.all(AppSpaces.smallPadding),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // * Fields
                ProfileFields(
                  controllers: controllers,
                  phoneVerified: phoneVerified,
                  showPasswordFields: changePassword,
                ),

                context.largeGap,

                // * Save
                Button(
                  isLoading: authController.isLoading,
                  loadingWidget: const LoadingWidget(),
                  label: context.tr.save,
                  onPressed: () {
                    final validatePhone = !phoneVerified.value &&
                        controllers[ApiStrings.phone]?.text !=
                            const UserModel().currentUser.phone &&
                        !changePassword;

                    if (validatePhone) {
                      context.showBarMessage(
                          context.tr.validateYourPhoneFirstPlease,
                          isError: true);
                      return;
                    }

                    if (!_formKey.value.currentState!.validate()) return;

                    if (changePassword) {
                      authController.updatePassword(
                        controllers: controllers,
                      );
                    } else {
                      authController.updateProfile(
                        controllers: controllers,
                        filePath: mediaController.filePath,
                      );
                    }
                  },
                )
              ],
            ),
          ),
        ),
      ),
    );
  }
}
