import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/auth/controllers/auth_controller.dart';
import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/auth/view/widgets/otp_bottom_sheet/otp_bottom_sheet.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/icon_widget/fields_icon_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class ProfileFields extends HookConsumerWidget {
  final Map<String, TextEditingController> controllers;
  final ValueNotifier<bool> phoneVerified;
  final bool showPasswordFields;

  const ProfileFields({
    super.key,
    required this.controllers,
    required this.phoneVerified,
    this.showPasswordFields = false,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authController = ref.watch(authControllerProvider(context));
    final isObscure = useState(true);

    void verify() async {
      final phone = '+2${controllers[ApiStrings.phone]?.text}';

      final phoneNotValid = phone.isEmpty || phone.length < 13;

      if (phoneNotValid) {
        context.showBarMessage(context.tr.enterValidPhoneNumber, isError: true);
        return;
      }

      await authController.signInWithPhoneNumber(
        phone: phone,
      );

      _showAuthBottomSheet(context, phone: phone);
    }

    final passwordFields = [
      context.largeGap,

      //! Password Field
      BaseTextField(
        title: context.tr.password,
        textInputType: TextInputType.visiblePassword,
        controller: controllers[ApiStrings.password],
        isObscure: isObscure.value,
        validator: (value) {
          return Validations.password(value);
        },
        suffixIcon: InkWell(
          onTap: () {
            isObscure.value = !isObscure.value;
          },
          child: const FieldsIconWidget(
            icon: Assets.iconsCloseEye,
          ),
        ),
      ),

      context.largeGap,

      //! Password Confirmation Field
      BaseTextField(
        title: context.tr.passwordConfirmation,
        textInputType: TextInputType.visiblePassword,
        controller: controllers[ApiStrings.passwordConfirmation],
        isObscure: isObscure.value,
        validator: (value) {
          // return Validations.password(value);

          if (controllers[ApiStrings.password]?.text != value) {
            return context.tr.passwordsDoNotMatch;
          }

          return null;
        },
        suffixIcon: InkWell(
          onTap: () {
            isObscure.value = !isObscure.value;
          },
          child: const FieldsIconWidget(
            icon: Assets.iconsCloseEye,
          ),
        ),
      ),
    ];

    final profileFields = [
      if (!showPasswordFields) ...[
        context.largeGap,
        //! Name Field
        BaseTextField(
          title: context.tr.name,
          controller: controllers[ApiStrings.name],
        ),
      ],

      context.largeGap,

      //! Phone Field
      StatefulBuilder(builder: (context, setState) {
        return BaseTextField(
          title: context.tr.phoneNumber,
          enabled: !phoneVerified.value,
          textInputType: TextInputType.number,
          controller: controllers[ApiStrings.phone],
          hint: '0113 xxxx xxxx',
          onChanged: (value) {
            if (value != const UserModel().currentUser.phone) {
              phoneVerified.value = false;
            }
            setState(() {});
          },
          icon: const FieldsIconWidget(
            icon: Assets.iconsPhone,
          ),
          suffixIcon: phoneVerified.value ||
                  controllers[ApiStrings.phone]?.text ==
                      const UserModel().currentUser.phone
              ? const Icon(
                  Icons.check_circle,
                  color: ColorManager.buttonColor,
                )
              : TextButton(
                  onPressed: verify,
                  child: Text(
                    context.tr.verify,
                    style: context.labelLarge.copyWith(
                        color: ColorManager.primaryColor,
                        fontWeight: FontWeight.bold),
                  ),
                ),
        );
      }),

      context.largeGap,

      //! Email Field
      BaseTextField(
        title: context.tr.email,
        controller: controllers[ApiStrings.email],
        textInputType: TextInputType.emailAddress,
      ),
    ];

    // * Check & View Fields ========================================
    return Column(
      children: [
        if (!showPasswordFields) ...[
          ...profileFields,
        ] else ...[
          ...passwordFields
        ]
      ],
    );
  }

  void _showAuthBottomSheet(BuildContext context, {String? phone}) {
    showModalBottomSheet(
        context: context,
        isScrollControlled: true,
        shape: const RoundedRectangleBorder(
            borderRadius: BorderRadius.only(
                topLeft: Radius.circular(AppRadius.baseRadius),
                topRight: Radius.circular(15))),
        builder: (ctx) =>
            OtpBottomSheet(phoneNumber: phone, phoneVerified: phoneVerified));
  }
}
