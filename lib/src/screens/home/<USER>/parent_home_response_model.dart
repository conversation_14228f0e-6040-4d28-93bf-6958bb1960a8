import 'package:connectify_app/src/screens/attendance/models/attendance_model.dart';
import 'package:connectify_app/src/screens/exam/models/exam_model.dart';
import 'package:connectify_app/src/screens/messages/model/messages_model.dart';
import 'package:connectify_app/src/screens/parent_activity/models/teacher_activity_model.dart';
import 'package:connectify_app/src/screens/supplies/model/teacher_supply_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';

import '../../announcement/model/announcement_model.dart';
import '../../supplies/model/supply_model.dart';

/// Response model for the combined parent home API
class ParentHomeResponseModel {
  final List<TeacherActivityModel> currentActivities;
  final List<TeacherActivityModel> todayActivities;
  final List<MessageModel> messages;
  final List<ExamModel> exams;
  final List<AttendanceModel> attendance;
  final List<AnnouncementModel> announcements;
  final List<TeacherSupplyModel> supplies;
  final ParentHomeMetaModel meta;

  const ParentHomeResponseModel({
    this.currentActivities = const [],
    this.todayActivities = const [],
    this.messages = const [],
    this.exams = const [],
    this.attendance = const [],
    this.announcements = const [],
    this.supplies = const [],
    required this.meta,
  });

  factory ParentHomeResponseModel.fromJson(Map<String, dynamic> json) {
    final data = json[ApiStrings.data] ?? {};
    final meta = json['meta'] ?? {};

    return ParentHomeResponseModel(
      currentActivities: (data['currentActivities'] as List?)
              ?.map((e) => TeacherActivityModel.fromJsonWithoutAttributes(e))
              .toList() ??
          [],
      todayActivities: (data['todayActivities'] as List?)
              ?.map((e) => TeacherActivityModel.fromJsonWithoutAttributes(e))
              .toList() ??
          [],
      messages: (data['messages'] as List?)
              ?.map((e) => MessageModel.fromJsonWithoutAttributes(e))
              .toList() ??
          [],
      exams: (data['exams'] as List?)
              ?.map((e) => ExamModel.fromJsonWithoutAttributes(e))
              .toList() ??
          [],
      attendance: (data['attendance'] as List?)
              ?.map((e) => AttendanceModel.fromJsonWithoutAttributes(e))
              .toList() ??
          [],
      announcements: (data['announcements'] as List?)
              ?.map((e) => AnnouncementModel.fromJsonWithoutAttributes(e))
              .toList() ??
          [],
      supplies: (data['supplies'] as List?)
              ?.map((e) => TeacherSupplyModel(
                    id: e['id'],
                    supplies: e['supplies'] != null
                        ? (e['supplies'] as List)
                            .map(
                                (e) => SupplyModel.fromJsonWithoutAttributes(e))
                            .toList()
                        : [],
                    markAsSent: e['markAsSent'] ?? false,
                  ))
              .toList() ??
          [],
      meta: ParentHomeMetaModel.fromJson(meta),
    );
  }

  /// Get filtered messages (excluding parent type)
  List<MessageModel> get filteredMessages {
    return messages.where((element) => element.type != 'parent').toList();
  }

  /// Get limited messages (first 2)
  List<MessageModel> get limitedMessages {
    return filteredMessages.take(2).toList();
  }

  /// Get limited announcements (first 2)
  List<AnnouncementModel> get limitedAnnouncements {
    return announcements.take(2).toList();
  }

  /// Check if student is present today
  bool get isStudentPresentToday {
    return attendance.isNotEmpty;
  }
}

class ParentHomeMetaModel {
  final String timestamp;
  final ParentHomeCountsModel counts;

  const ParentHomeMetaModel({
    required this.timestamp,
    required this.counts,
  });

  factory ParentHomeMetaModel.fromJson(Map<String, dynamic> json) {
    return ParentHomeMetaModel(
      timestamp: json['timestamp'] ?? '',
      counts: ParentHomeCountsModel.fromJson(json['counts'] ?? {}),
    );
  }
}

class ParentHomeCountsModel {
  final int currentActivities;
  final int todayActivities;
  final int messages;
  final int exams;
  final int attendance;
  final int announcements;
  final int supplies;

  const ParentHomeCountsModel({
    this.currentActivities = 0,
    this.todayActivities = 0,
    this.messages = 0,
    this.exams = 0,
    this.attendance = 0,
    this.announcements = 0,
    this.supplies = 0,
  });

  factory ParentHomeCountsModel.fromJson(Map<String, dynamic> json) {
    return ParentHomeCountsModel(
      currentActivities: json['currentActivities'] ?? 0,
      todayActivities: json['todayActivities'] ?? 0,
      messages: json['messages'] ?? 0,
      exams: json['exams'] ?? 0,
      attendance: json['attendance'] ?? 0,
      announcements: json['announcements'] ?? 0,
      supplies: json['supplies'] ?? 0,
    );
  }
}
