import 'package:connectify_app/src/screens/plan/view/plans_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class HomePlansWidget extends StatelessWidget {
  const HomePlansWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return BaseContainer(
      child: ListTile(
        contentPadding: EdgeInsets.zero,
        title: Text(
          context.tr.plans,
          style: context.boldTitle,
        ),
        trailing: Icon(
          Icons.chevron_right,
          color: context.isEng ? Colors.grey[600] : Colors.grey[600],
          size: 24,
        ),
        onTap: () => context.to(const PlansScreen()),
      ),
    );
  }
}
