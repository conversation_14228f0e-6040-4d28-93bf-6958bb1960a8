import 'package:connectify_app/src/screens/home/<USER>/indicator_controller.dart';
import 'package:connectify_app/src/screens/parent_activity/models/teacher_activity_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:dots_indicator/dots_indicator.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../shared/widgets/shared_widgets.dart';
import 'current_activity_card.dart';

class CurrentActivity extends ConsumerWidget {
  final List<TeacherActivityModel> teacherActivities;
  const CurrentActivity({super.key, required this.teacherActivities});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final currentIndex = ref.watch(indicatorControllerProvider);
    final indicatorCtrl = ref.watch(indicatorController);

    return Column(
      children: [
        BaseContainer(
            boxShadow: ConstantsWidgets.boxShadow,
            radius: AppRadius.sliderRadius,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                //! Current Activity Text
                Text(
                  context.tr.currentActivity,
                  style: context.boldTitle,
                ),

                context.mediumGap,

                if (teacherActivities.isEmpty)
                  Center(
                    child: Text(
                      context.tr.noActivities,
                      style: context.title,
                    ),
                  ),

                //! Current Activity List
                if (teacherActivities.isNotEmpty) ...[
                  SizedBox(
                    height: 75.h,
                    width: double.infinity,
                    child: PageView(
                      children: teacherActivities.map((e) {
                        return CurrentActivityCard(
                            classModel: e.classModel!, teacherActivity: e);
                      }).toList(),
                      onPageChanged: (index) {
                        indicatorCtrl.changeIndex(index);
                      },
                    ),
                  ),

                  //! Indicator
                  Center(
                    child: DotsIndicator(
                      dotsCount: teacherActivities.length,
                      position: currentIndex,
                      mainAxisSize: MainAxisSize.min,
                      reversed: false,
                      decorator: DotsDecorator(
                        size: const Size.square(9.0),
                        activeSize: const Size(18.0, 9.0),
                        color: ColorManager.greyIndicator,
                        activeColor: ColorManager.purple,
                        activeShape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(5.0)),
                      ),
                    ),
                  ),
                ]
              ],
            )),
      ],
    );
  }
}
