import 'package:connectify_app/src/screens/messages/model/messages_model.dart';
import 'package:connectify_app/src/screens/messages/view/widgets/home_messages_list.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:xr_helper/xr_helper.dart';

class HomeMessages extends StatelessWidget {
  final List<MessageModel> messagesList;
  const HomeMessages({super.key, required this.messagesList});

  @override
  Widget build(BuildContext context) {
    return BaseContainer(
        padding: 0,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              context.tr.messages,
              style: context.boldTitle,
            ).paddingAll(AppSpaces.mediumPadding),
            HomeMessagesList(
              messagesList: messagesList,
            ),
          ],
        ));
  }
}
