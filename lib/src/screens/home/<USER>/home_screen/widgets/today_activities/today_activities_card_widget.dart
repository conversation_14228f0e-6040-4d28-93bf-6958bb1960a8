import 'package:connectify_app/src/screens/parent_activity/models/teacher_activity_model.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../../generated/assets.dart';
import '../../../../../../shared/widgets/shared_widgets.dart';

class TodayActivitiesCardWidget extends StatelessWidget {
  final TeacherActivityModel teacherActivity;
  const TodayActivitiesCardWidget({super.key, required this.teacherActivity});

  @override
  Widget build(
    BuildContext context,
  ) {
    return BaseContainer(
      child: Row(
        children: [
          SizedBox(
            height: 50.h,
            width: 55.w,
            child: ClipRRect(
              borderRadius:
                  BorderRadius.circular(AppRadius.baseContainerRadius),
              child: BaseCachedImage(
                teacherActivity.activity?.image?.url ?? '',
                fit: BoxFit.cover,
                errorWidget: Image.asset(
                  Assets.imagesActivities,
                ),
              ),
            ),
          ),
          context.smallGap,
          Expanded(
            flex: 4,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  teacherActivity.activity?.name ?? '',
                  style: context.blueHint,
                  maxLines: 1,
                ),
                context.xSmallGap,
                Text(
                  teacherActivity.activity?.description ?? '',
                  style: context.smallHint.copyWith(fontSize: 12),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ),
          const Spacer(),
          Text('${teacherActivity.startTime} / ${teacherActivity.endTime}',
              style: context.subTitle.copyWith(fontWeight: FontWeight.bold))
        ],
      ),
    );
  }
}
