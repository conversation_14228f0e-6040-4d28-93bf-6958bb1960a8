import 'package:connectify_app/src/screens/home/<USER>/home_screen/widgets/today_activities/today_activities_card_widget.dart';
import 'package:connectify_app/src/screens/parent_activity/models/teacher_activity_model.dart';
import 'package:connectify_app/src/screens/parent_activity/view/parent_activity_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/app_date_time.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class TodayActivities extends HookConsumerWidget {
  final List<TeacherActivityModel> teacherActivities;

  const TodayActivities({super.key, required this.teacherActivities});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return BaseContainer(
        child: Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          context.tr.todaysActivities,
          style: context.boldTitle,
        ),
        context.largeGap,
        Builder(builder: (context) {
          final dayNumber = AppDateTime.dayName;

          final listLimit = teacherActivities.take(2).toList();

          final filterListByDay =
              listLimit.where((element) => element.day == dayNumber).toList();

          if (filterListByDay.isEmpty) {
            return Padding(
              padding: const EdgeInsets.all(AppSpaces.largePadding),
              child: Center(
                child: Text(context.tr.noActivitiesToday,
                    style: context.title.copyWith(fontWeight: FontWeight.bold)),
              ),
            );
          }

          return ListView.separated(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: filterListByDay.length,
            itemBuilder: (context, index) {
              final teacherActivityModel = filterListByDay[index];

              return TodayActivitiesCardWidget(
                teacherActivity: teacherActivityModel,
              );
            },
            separatorBuilder: (BuildContext context, int index) {
              return context.mediumGap;
            },
          );
        }),
        context.mediumGap,
        Center(
            child: TextButton(
                onPressed: () => context.to(const ParentActivitiesScreen()),
                child: Text(context.tr.seeAllActivities,
                    style: context.boldTitle)))
      ],
    ));
  }
}
