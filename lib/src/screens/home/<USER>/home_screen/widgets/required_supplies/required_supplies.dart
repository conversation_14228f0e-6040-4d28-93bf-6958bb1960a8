import 'package:connectify_app/src/screens/home/<USER>/home_screen/widgets/required_supplies/required_suppliesL_list.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../../shared/widgets/shared_widgets.dart';
import '../../../../controllers/bottom_nav_controller.dart';

class RequiredSupplies extends ConsumerWidget {
  const RequiredSupplies({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Consumer(
      builder: (context, ref, child) {
        final bottomNavBarAddWidget = ref.watch(bottomNavController);
        return BaseContainer(
            boxShadow: ConstantsWidgets.boxShadow,
            radius: AppRadius.sliderRadius,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                //! Current Activity Text
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      context.tr.requiredSupplies,
                      style: context.boldTitle,
                    ),
                    TextButton(
                      onPressed: () {
                        bottomNavBarAddWidget.changeIndex(1);
                      },
                      child: Text(
                        context.tr.seeAll,
                        style: const TextStyle(color: Colors.blueAccent),
                      ),
                    ),
                  ],
                ),
                const RequiredSuppliesList(),
              ],
            ));
      },
    );
  }
}
