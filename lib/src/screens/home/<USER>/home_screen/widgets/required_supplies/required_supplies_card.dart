import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/screens/supplies/controller/teacher_supply_controller.dart';
import 'package:connectify_app/src/screens/supplies/model/teacher_supply_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/shared_widgets.dart';
import 'package:connectify_app/src/shared/widgets/text_button/text_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class RequiredSuppliesCard extends HookConsumerWidget {
  final TeacherSupplyModel supply;

  const RequiredSuppliesCard({super.key, required this.supply});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final isSent = supply.markAsSent ?? false;
    final suppliesCtrlProvider =
        ref.watch(teacherSupplyControllerChangeNotifierProvider(context));

    final isMarkedAsSent = useState(isSent);

    if (isMarkedAsSent.value) {
      return const SizedBox();
    }

    return Container(
      padding: const EdgeInsets.all(AppSpaces.mediumPadding),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(AppRadius.bottomNavRadius - 3),
        color: Colors.white,
        border: Border.all(color: Colors.black, width: .5),
      ),
      child: FittedBox(
        child: Column(
          children: [
            //! Image ---------------------
            Container(
              decoration: BoxDecoration(
                  color: ColorManager.lightGrey,
                  borderRadius:
                      BorderRadius.circular(AppRadius.baseContainerRadius)),
              child: BaseCachedImage(
                supply.image?.url ?? '',
                height: 50,
                errorWidget: Image.asset(Assets.imagesSupplies),
              ),
            ),

            context.mediumGap,

            //! Supply Name ---------------------
            Text(
              supply.supplies
                      ?.map(
                        (e) => e.name,
                      )
                      .join(' -\n') ??
                  '',
              style: context.body,
            ),

            context.mediumGap,
            if (isMarkedAsSent.value)
              Text(
                context.tr.sent,
                style: context.title.copyWith(color: ColorManager.successColor),
              ).paddingAll(AppSpaces.smallPadding)
            else
              //! Mark As Sent ---------------------
              BaseTextButton(
                  title: context.tr.markAsSent,
                  onTap: () async {
                    isMarkedAsSent.value = true;
                    await suppliesCtrlProvider.editTeacherSupply(
                        navigationWidget: const MainScreen(), id: supply.id!);
                  })
          ],
        ),
      ),
    );
  }
}
