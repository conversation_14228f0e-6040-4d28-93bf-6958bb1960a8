import 'package:connectify_app/src/screens/supplies/controller/teacher_supply_controller.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import 'required_supplies_card.dart';

class RequiredSuppliesList extends ConsumerWidget {
  const RequiredSuppliesList({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final suppliesCtrl = ref.watch(getTeacherSupplyDataProvider(context));

    return Container(
        margin: const EdgeInsets.symmetric(vertical: 20.0),
        height: 200.0,
        child: suppliesCtrl.get(
          data: (supplies) {
            final isAllSuppliesContainsMarkedAsSent =
                supplies.every((element) => element.markAsSent ?? false);

            if (supplies.isEmpty || isAllSuppliesContainsMarkedAsSent) {
              return Text(
                context.tr.noSupplies,
                style: context.subHeadLine,
              ).center();
            }

            return ListView.separated(
              scrollDirection: Axis.horizontal,
              itemBuilder: (context, index) => RequiredSuppliesCard(
                supply: supplies[index],
              ),
              separatorBuilder: (context, index) => context.largeGap,
              itemCount: supplies.length,
            );

            // ListView.separated(
            //   scrollDirection: Axis.horizontal,
            //   itemBuilder: (context, index) => RequiredSuppliesCard(
            //         supply: suppliesLimit[index],
            //       ),
            //   separatorBuilder: (context, index) => context.largeGap,
            //   itemCount: suppliesLimit.length);
          },
        ));
  }
}

// ! List
