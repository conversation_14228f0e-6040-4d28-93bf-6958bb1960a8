import 'package:connectify_app/src/screens/announcement/controller/announcement_controller.dart';
import 'package:connectify_app/src/screens/announcement/view/announcements_screen.dart';
import 'package:connectify_app/src/screens/home/<USER>/home_screen/widgets/home_announcements/home_announcement_card_widget.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

class HomeAnnouncementsWidget extends HookConsumerWidget {
  const HomeAnnouncementsWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final announcementsAsync = ref.watch(getAnnouncementsDataProvider(context));

    return BaseContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            context.tr.announcements,
            style: context.boldTitle,
          ),
          context.largeGap,
          announcementsAsync.get(
            data: (announcements) {
              if (announcements.isEmpty) {
                return Padding(
                  padding: const EdgeInsets.all(AppSpaces.largePadding),
                  child: Center(
                    child: Text(
                      context.tr.noAnnouncementsToday,
                      style:
                          context.title.copyWith(fontWeight: FontWeight.bold),
                    ),
                  ),
                );
              }

              // Take only the last 2 announcements (most recent)
              final recentAnnouncements = announcements.take(2).toList();

              return SizedBox(
                height: 120,
                child: ListView.separated(
                  scrollDirection: Axis.horizontal,
                  itemCount: recentAnnouncements.length,
                  itemBuilder: (context, index) {
                    final announcement = recentAnnouncements[index];
                    return SizedBox(
                      width: MediaQuery.of(context).size.width * 0.8,
                      child: HomeAnnouncementCardWidget(
                        announcement: announcement,
                      ),
                    );
                  },
                  separatorBuilder: (context, index) => context.mediumGap,
                ),
              );
            },
          ),
          context.mediumGap,
          Center(
            child: TextButton(
              onPressed: () => context.to(const AnnouncementsScreen()),
              child: Text(
                context.tr.seeAllAnnouncements,
                style: context.boldTitle,
              ),
            ),
          ),
        ],
      ),
    );
  }
}
