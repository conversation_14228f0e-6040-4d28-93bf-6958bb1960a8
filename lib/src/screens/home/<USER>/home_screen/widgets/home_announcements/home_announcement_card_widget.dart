import 'package:connectify_app/src/screens/announcement/model/announcement_model.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:xr_helper/xr_helper.dart';

class HomeAnnouncementCardWidget extends StatelessWidget {
  final AnnouncementModel announcement;
  
  const HomeAnnouncementCardWidget({
    super.key, 
    required this.announcement,
  });

  @override
  Widget build(BuildContext context) {
    return BaseContainer(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header with date and target badge
          Row(
            children: [
              if (announcement.createdAt != null)
                Expanded(
                  child: Text(
                    announcement.createdAt!.formatDateToTimeAndString,
                    style: context.labelSmall.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ),
              context.smallGap,
              // Target badge
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: _getTargetColor(announcement.target),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  context.isEng
                      ? announcement.target.displayName
                      : announcement.target.displayNameAr,
                  style: context.labelSmall.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w500,
                    fontSize: 10.sp,
                  ),
                ),
              ),
            ],
          ),
          
          context.smallGap,
          
          // Title
          Text(
            announcement.title,
            style: context.blueHint,
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
          
          context.xSmallGap,
          
          // Description
          Text(
            announcement.description,
            style: context.smallHint.copyWith(fontSize: 12),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  Color _getTargetColor(AnnouncementTarget target) {
    switch (target) {
      case AnnouncementTarget.admins:
        return Colors.red;
      case AnnouncementTarget.teachers:
        return Colors.blue;
      case AnnouncementTarget.parents:
        return Colors.green;
      case AnnouncementTarget.all:
        return Colors.purple;
    }
  }
}
