import 'package:flutter_riverpod/flutter_riverpod.dart';

// * Indicator Nav Bar Controller ========================================
final indicatorController = Provider<IndicatorController>(
  (ref) {
    return IndicatorController();
  },
);

// * Indicator Nav Bar State Notifier ========================================
final indicatorControllerProvider =
    StateNotifierProvider<IndicatorController, int>(
  (ref) => ref.watch(indicatorController),
);

class IndicatorController extends StateNotifier<int> {
  IndicatorController() : super(0);

  void changeIndex(int index) {
    state = index;
  }
}
