import 'package:flutter_riverpod/flutter_riverpod.dart';

// * Bottom Nav Bar Controller ========================================
final bottomNavController = Provider<BottomNavigationController>(
  (ref) {
    return BottomNavigationController();
  },
);

// * Bottom Nav Bar State Notifier ========================================
final bottomNavigationControllerProvider =
    StateNotifierProvider<BottomNavigationController, int>(
  (ref) => ref.watch(bottomNavController),
);

class BottomNavigationController extends StateNotifier<int> {
  BottomNavigationController() : super(0);

  void changeIndex(int index) {
    state = index;
  }
}
