import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/attendance/view/attendance/attendance_screen.dart';
import 'package:connectify_app/src/screens/emergency/view/emergency_screen.dart';
import 'package:connectify_app/src/screens/events/view/events/events_screen.dart';
import 'package:connectify_app/src/screens/exam/view/exam_screen.dart';
import 'package:connectify_app/src/screens/parent_activity/view/parent_activity_screen.dart';
import 'package:connectify_app/src/screens/supplies/view/admin_supplies/supplies_screen.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../student/controllers/student_controller.dart';
import '../../student/view/student_screen/widgets/add_student.dart';
import '../view/main_screen.dart';

class HomeAddModel {
  final String image;
  final String title;
  final String subTitle;
  final Function() onTap;

  const HomeAddModel({
    required this.image,
    required this.onTap,
    required this.title,
    required this.subTitle,
  });

  static List<HomeAddModel> homeList(BuildContext context, WidgetRef ref) => [
        HomeAddModel(
            onTap: () => context.to(const ParentActivitiesScreen()),
            image: Assets.svgParentActivities,
            title: context.tr.activities,
            subTitle: context.tr.activities),
        HomeAddModel(
            onTap: () => context.to(const SuppliesScreen()),
            image: Assets.svgSupplies,
            title: context.tr.supplies,
            subTitle: context.tr.supplies),
        HomeAddModel(
          onTap: () async {
            final studentController =
                ref.watch(studentChangeNotifierProvider(context));

            final student = await studentController.getStudentById();

            showAddStudentDialog(
              context,
              ref: ref,
              navigateWidget: const MainScreen(),
              student: student,
            );
          },
          image: Assets.svgCar,
          title: context.tr.pickupPersons,
          subTitle: context.tr.pickupPersons,
        ),
        HomeAddModel(
          onTap: () {
            context.to(const ExamScreen());
          },
          image: Assets.svgExam,
          title: context.tr.exams,
          subTitle: '\n',
        ),
      ];

  static List<HomeAddModel> homeList2(BuildContext context) => [
        HomeAddModel(
            onTap: () => context.to(const AttendanceScreen()),
            image: Assets.svgParentAttendance,
            title: context.tr.attendance,
            subTitle: context.tr.attendance),
        HomeAddModel(
            onTap: () => context.to(const EventsScreen()),
            image: Assets.svgParentEvents,
            title: context.tr.events,
            subTitle: context.tr.eventThisMonth),
        HomeAddModel(
            onTap: () => context.to(const EmergencyScreen()),
            image: Assets.svgParentEmergency,
            title: context.tr.emergency,
            subTitle: '\n'),
      ];
}
