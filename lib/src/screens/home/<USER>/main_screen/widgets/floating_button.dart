import 'package:connectify_app/generated/assets.dart';
import 'package:connectify_app/src/screens/home/<USER>/main_screen/widgets/send_message_fields.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/widgets/icon_widget/icon_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:flutter_svg/svg.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../../../../shared/widgets/shared_widgets.dart';
import '../../../../messages/controller/messages_controller.dart';
import '../../../../parent_activity/view/parent_activity_screen.dart';
import '../../../model/home_add_model.dart';

class FloatingButtonWidget extends HookWidget {
  const FloatingButtonWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.bottomCenter,
      child: Container(
        margin: EdgeInsets.only(bottom: 10.h),
        height: 50.h,
        width: 50.w,
        decoration: const BoxDecoration(
          color: ColorManager.primaryColor,
          shape: BoxShape.circle,
        ),
        child: const Padding(
          padding: EdgeInsets.all(AppSpaces.smallPadding + 3),
          child: IconWidget(icon: Assets.svgParentChat),
        ),
      ),
    ).onTap(() {
      showSendMessageDialog(context,
          navigateWidget: const ParentActivitiesScreen());
    });
  }
}

class BottomNavBarAddWidget extends StatelessWidget {
  final HomeAddModel home;
  final bool isLast;
  final Function() onTap;

  const BottomNavBarAddWidget(
      {super.key,
      required this.home,
      required this.isLast,
      required this.onTap});

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Row(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Expanded(
            child: Column(
              children: [
                SizedBox(
                    height: 30.h,
                    width: 30.w,
                    child: SvgPicture.asset(home.image)),
                FittedBox(
                  fit: BoxFit.scaleDown,
                  child: Text(
                    home.title,
                    style: context.hint,
                  ),
                ),
              ],
            ),
          ),
          Container(
            height: 80,
            color: ColorManager.black.withOpacity(0.5),
            width: .5,
          )
        ],
      ),
    );
  }
}

Future<void> showSendMessageDialog(BuildContext context,
    {required Widget navigateWidget, bool isAdmin = true}) async {
  showDialog(
      context: context,
      builder: (context) {
        return HookConsumer(
          builder: (context, ref, child) {
            final messageController =
                ref.watch(messageChangeNotifierControllerProvider(context));
            final controllers = {
              ApiStrings.title: useTextEditingController(),
              ApiStrings.description: useTextEditingController(),
            };

            final selectedValue = useState(isAdmin ? 'Admin' : 'Teacher');

            //!-----------------------------------------------------

            final formKey = useState(GlobalKey<FormState>());

            return AlertDialogWidget(
                iconPath: Assets.imagesLogo,
                isImage: false,
                header: context.tr.sendANewMessage,
                isLoading: messageController.isLoading,
                child: Form(
                  key: formKey.value,
                  child: SendMessageFields(
                    selectedValue: selectedValue,
                    controllers: controllers,
                    isAdmin: isAdmin,
                  ),
                ),
                onConfirm: () async {
                  if (formKey.value.currentState!.validate()) {
                    await messageController.sendMessage(
                      ref,
                      title: controllers[ApiStrings.title]!.text,
                      description: controllers[ApiStrings.description]!.text,
                      isAdmin: selectedValue.value == 'Admin',
                    );
                  }
                });
          },
        );
      });
}
