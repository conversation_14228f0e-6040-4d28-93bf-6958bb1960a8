import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/shared/data/remote/api_strings.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:xr_helper/xr_helper.dart';

class SendMessageFields extends HookWidget {
  final bool isAdmin;
  final Map<String, TextEditingController> controllers;
  final ValueNotifier<String> selectedValue;

  const SendMessageFields({
    super.key,
    required this.controllers,
    required this.selectedValue,
    this.isAdmin = true,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // ! Base Drop Down With Admin And teacher
        BaseDropDown(
          title: context.tr.to,
          data: [
            "Admin",
            if (NurseryModelHelper.currentNursery()?.canContactTeacher == true)
              "Teacher",
          ],
          selectedValue: selectedValue.value,
          icon: const Icon(Icons.person),
          onChanged: (value) {
            selectedValue.value = value;
          },
        ),

        context.fieldsGap,

        //! Title
        BaseTextField(
          controller: controllers[ApiStrings.title],
          title: context.tr.title,
          textInputType: TextInputType.text,
        ),

        context.fieldsGap,

        //! Message
        BaseTextField(
          controller: controllers[ApiStrings.description],
          title: context.tr.message,
          textInputType: TextInputType.text,
          maxLines: 4,
          isRequired: false,
        ),
      ],
    );
  }
}
