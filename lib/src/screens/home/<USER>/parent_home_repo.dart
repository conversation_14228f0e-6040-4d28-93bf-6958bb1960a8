import 'package:connectify_app/src/screens/home/<USER>/parent_home_response_model.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

// * =========================================================

final parentHomeRepoProvider = Provider((ref) {
  final networkApiServices = ref.watch(networkServiceProvider);
  return ParentHomeRepo(networkApiServices);
});

class ParentHomeRepo with BaseRepository {
  final BaseApiServices _networkApiServices;

  ParentHomeRepo(this._networkApiServices);

  //? Get Parent Home Data for Today ========================================================
  Future<ParentHomeResponseModel> getParentHomeTodayData() async {
    return await baseFunction(() async {
      // The API will automatically filter for today's data based on current time
      // No additional filters needed as the API handles this internally
      final url = ApiEndpoints.parentHome();

      final response = await _networkApiServices.getResponse(url);

      final parentHomeData = ParentHomeResponseModel.fromJson(response);

      return parentHomeData;
    });
  }
}
