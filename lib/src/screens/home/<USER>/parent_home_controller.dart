import 'package:connectify_app/src/screens/home/<USER>/parent_home_response_model.dart';
import 'package:connectify_app/src/screens/home/<USER>/parent_home_repo.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

// * =========================================================

final parentHomeControllerProvider =
    ChangeNotifierProvider.family<ParentHomeController, BuildContext>(
        (ref, context) {
  final parentHomeRepo = ref.watch(parentHomeRepoProvider);

  return ParentHomeController(
    parentHomeRepo: parentHomeRepo,
    context: context,
  );
});

// Provider for getting parent home data
final getParentHomeDataProvider =
    FutureProvider.family<ParentHomeResponseModel, BuildContext>(
        (ref, context) {
  final parentHomeRepo = ref.watch(parentHomeRepoProvider);

  final controller = ParentHomeController(
    parentHomeRepo: parentHomeRepo,
    context: context,
  );

  return controller.getParentHomeTodayData();
});

class ParentHomeController extends BaseVM {
  final ParentHomeRepo parentHomeRepo;
  final BuildContext context;

  ParentHomeController({
    required this.parentHomeRepo,
    required this.context,
  });

  Future<ParentHomeResponseModel> getParentHomeTodayData() async {
    return await baseFunction(context, () async {
      final parentHomeData = await parentHomeRepo.getParentHomeTodayData();

      return parentHomeData;
    });
  }
}
