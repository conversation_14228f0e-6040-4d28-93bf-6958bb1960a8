import 'package:connectify_app/src/screens/home/<USER>/main_screen.dart';
import 'package:connectify_app/src/screens/notification/controller/notification_controller.dart';
import 'package:connectify_app/src/screens/notification/model/notification_model.dart';
import 'package:connectify_app/src/shared/extensions/context_extensions.dart';
import 'package:connectify_app/src/shared/extensions/riverpod_extensions.dart';
import 'package:connectify_app/src/shared/widgets/base_container.dart';
import 'package:connectify_app/src/shared/widgets/base_list/base_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter_hooks/flutter_hooks.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:hooks_riverpod/hooks_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../../home/<USER>/main_screen/widgets/main_app_bar.dart';

class NotificationScreen extends HookConsumerWidget {
  const NotificationScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final page = useState<int>(1);
    final isLoadingMore = useState<bool>(false);
    final isInitialLoadComplete = useState<bool>(false);
    final notifications = useState<List<NotificationModel>>([]);

    final params = (context, page.value);

    useEffect(() {
      ref.refresh(getNotificationDataProviderWithPagination(params));
      return () {};
    }, [page.value]);

    ref.listenPagination<NotificationModel>(
      provider: getNotificationDataProviderWithPagination(params),
      dataNotifier: notifications,
      isLoadingNotifier: isLoadingMore,
      isInitialLoadCompleteNotifier: isInitialLoadComplete,
    );

    return WillPopScope(
      onWillPop: () async {
        context.toReplacement(const MainScreen());
        return false;
      },
      child: Scaffold(
        appBar: MainAppBar(
          isBackButton: true,
          title: context.tr.notifications,
          iconPath: '',
        ),
        body: BaseList(
          page: page,
          isLoading: !isInitialLoadComplete.value,
          padding: const EdgeInsets.all(AppSpaces.mediumPadding),
          isLoadingMore: isLoadingMore,
          data: notifications.value,
          itemBuilder: (notification, index) =>
              _NotificationWidget(notification: notification),
        ),
      ),
    );
  }
}

class _NotificationWidget extends StatelessWidget {
  final NotificationModel notification;

  const _NotificationWidget({required this.notification});

  @override
  Widget build(BuildContext context) {
    return BaseContainer(
      child: Row(
        children: [
          //! Circle & notification icon
          Container(
              margin: const EdgeInsets.only(right: AppSpaces.mediumPadding),
              height: 50.h,
              width: 50.w,
              decoration: const BoxDecoration(
                  shape: BoxShape.circle,
                  gradient: LinearGradient(
                      begin: Alignment.bottomLeft,
                      end: Alignment.topRight,
                      colors: [
                        Color(0xffccd3db),
                        Color(0xFFA6ADB7),
                      ])),
              child: const Icon(
                Icons.notifications,
                color: ColorManager.white,
                size: 25,
              )),

          context.mediumGap,

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              mainAxisSize: MainAxisSize.min,
              children: [
                //! New order from costumer
                Text(
                  notification.title,
                  style: context.labelLarge,
                ),

                context.smallGap,

                //! Date & time
                Text(
                  notification.body,
                  style: context.greyLabelMedium,
                )
              ],
            ),
          )
        ],
      ),
    );
  }
}

// class NotificationScreen extends HookConsumerWidget {
//   const NotificationScreen({super.key});
//
//   @override
//   Widget build(BuildContext context, WidgetRef ref) {
//     final notificationCtrl = ref.watch(getNotificationData(context));
//     return WillPopScope(
//       onWillPop: () async {
//         context.toReplacement(const MainScreen());
//         return false;
//       },
//       child: Scaffold(
//         appBar: MainAppBar(
//           isBackButton: true,
//           title: context.tr.notifications,
//           iconPath: '',
//         ),
//         body: notificationCtrl.get(
//           data: (notifications) {
//             if (notifications.isEmpty) {
//               return Center(
//                 child: Text(context.tr.noNotifications),
//               );
//             }
//
//             return HookBuilder(builder: (context) {
//               useEffect(() {
//                 GetStorageService.setLocalData(
//                   key: LocalKeys.notificationCount,
//                   value: notifications.length,
//                 );
//
//                 return () {};
//               }, []);
//               return ListView.separated(
//                   padding: const EdgeInsets.all(AppSpaces.mediumPadding),
//                   itemBuilder: (context, index) => _NotificationWidget(
//                         notification: notifications[index],
//                       ),
//                   separatorBuilder: (context, index) => context.mediumGap,
//                   itemCount: notifications.length);
//             });
//           },
//         ),
//       ),
//     );
//   }
// }
//
// class _NotificationWidget extends StatelessWidget {
//   final NotificationModel notification;
//
//   const _NotificationWidget({required this.notification});
//
//   @override
//   Widget build(BuildContext context) {
//     return BaseContainer(
//       child: Row(
//         children: [
//           //! Circle & notification icon
//           Container(
//               margin: const EdgeInsets.only(right: AppSpaces.mediumPadding),
//               height: 50.h,
//               width: 50.w,
//               decoration: const BoxDecoration(
//                   shape: BoxShape.circle,
//                   gradient: LinearGradient(
//                       begin: Alignment.bottomLeft,
//                       end: Alignment.topRight,
//                       colors: [
//                         Color(0xffccd3db),
//                         Color(0xFFA6ADB7),
//                       ])),
//               child: const Icon(
//                 Icons.notifications,
//                 color: ColorManager.white,
//                 size: 25,
//               )),
//
//           Expanded(
//             child: Column(
//               crossAxisAlignment: CrossAxisAlignment.start,
//               mainAxisSize: MainAxisSize.min,
//               children: [
//                 //! New order from costumer
//                 Text(
//                   notification.title,
//                   style: context.labelLarge,
//                 ),
//
//                 context.smallGap,
//
//                 //! Date & time
//                 Text(
//                   notification.body,
//                   style: context.greyLabelMedium,
//                 )
//               ],
//             ),
//           )
//         ],
//       ),
//     );
//   }
// }
