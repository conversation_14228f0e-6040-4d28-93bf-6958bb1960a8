import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/notification/repo/notification_repo.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../model/notification_model.dart';

final notificationControllerProvider =
    Provider.family<NotificationController, BuildContext>((ref, context) {
  final notificationRepo = ref.watch(notificationRepoProvider);

  return NotificationController(
      notificationRepo: notificationRepo, context: context);
});

final getNotificationData =
    FutureProvider.family<List<NotificationModel>, BuildContext>(
        (ref, context) {
  final notificationController =
      ref.watch(notificationControllerProvider(context));

  return notificationController.getNotification();
});

final getNotificationDataProviderWithPagination =
    FutureProvider.family<List<NotificationModel>, (BuildContext, int)>(
  (ref, params) async {
    final context = params.$1;
    final page = params.$2;

    final notificationCtrl = ref.watch(notificationControllerProvider(context));

    return await notificationCtrl.getNotificationsPaginated(page: page);
  },
);

class NotificationController extends BaseVM {
  final NotificationRepo notificationRepo;
  final BuildContext context;

  NotificationController({
    required this.notificationRepo,
    required this.context,
  });

  Future<List<NotificationModel>> getNotification() async {
    return await baseFunction(context, () async {
      final notificationData = await notificationRepo.getNotification();

      final filterNotificationData = notificationData
          .where((element) =>
              element.topic == NurseryModelHelper.allParentTopic() ||
              element.topic ==
                  NurseryModelHelper.parentByStudentTopic(
                      UserModel.studentId()))
          .toList();

      return filterNotificationData;
    });
  }

  //getNotificationsPaginated
  Future<List<NotificationModel>> getNotificationsPaginated(
      {required int page}) async {
    return await baseFunction(context, () async {
      final notificationData =
          await notificationRepo.getNotificationsPaginated(page: page);

      return notificationData;
    });
  }
}
