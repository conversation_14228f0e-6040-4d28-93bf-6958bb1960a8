import 'package:connectify_app/src/screens/auth/models/user_model.dart';
import 'package:connectify_app/src/screens/nursery/models/nursery_model_helper.dart';
import 'package:connectify_app/src/shared/data/remote/api_endpoints.dart';
import 'package:connectify_app/src/shared/providers/network_api_service_provider.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:xr_helper/xr_helper.dart';

import '../model/notification_model.dart';

final notificationRepoProvider = Provider((ref) {
  final networkApiServices = ref.watch(networkServiceProvider);

  return NotificationRepo(networkApiServices);
});

// * =============================================================

class NotificationRepo with BaseRepository {
  final BaseApiServices networkApiServices;

  NotificationRepo(this.networkApiServices);

  Future<List<NotificationModel>> getNotification() async {
    return await baseFunction(() async {
      final String allParentTopic = NurseryModelHelper.allParentTopic();
      final String parentByStudentTopic =
          NurseryModelHelper.parentByStudentTopic(UserModel.studentId());

      final response = await networkApiServices.getResponse(
          '${ApiEndpoints.notification}&filters[\$or][0][topic]=$allParentTopic&filters[\$or][1][topic]=$parentByStudentTopic');

      // final notificationData =
      //     (response[ApiStrings.data] as List)
      //         .map((e) => NotificationModel.fromJson(e))
      //         .toList();
      final notificationData =
          await compute(responseFromNotificationModel, response);
      return notificationData;
    });
  }

  //getNotificationPaginated
  Future<List<NotificationModel>> getNotificationsPaginated({
    int page = 1,
  }) async {
    return await baseFunction(() async {
      final String allParentTopic = NurseryModelHelper.allParentTopic();
      final String parentByStudentTopic =
          NurseryModelHelper.parentByStudentTopic(UserModel.studentId());

      final response = await networkApiServices.getResponse(
        '${ApiEndpoints.notification}&${ApiEndpoints.pagination(page)}&filters[\$or][0][topic]=$allParentTopic&filters[\$or][1][topic]=$parentByStudentTopic',
      );

      final notificationData =
          await compute(responseFromNotificationModel, response);

      return notificationData;
    });
  }
}
