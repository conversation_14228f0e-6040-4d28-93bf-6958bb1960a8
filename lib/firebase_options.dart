// File generated by FlutterFire CLI.
// ignore_for_file: lines_longer_than_80_chars, avoid_classes_with_only_static_members
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyD5Nr5S4wMFz8Q-79-ItXapQbSKD4wHaDk',
    appId: '1:296762082745:web:baddf4fbb087f7689da268',
    messagingSenderId: '296762082745',
    projectId: 'connectify-e5692',
    authDomain: 'connectify-e5692.firebaseapp.com',
    storageBucket: 'connectify-e5692.appspot.com',
    measurementId: 'G-WSXW2TWKLX',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyBKsmoKssvNqrkdaQbxx7FxzsOXQ1klymA',
    appId: '1:296762082745:android:2d5f055a3c3f4a719da268',
    messagingSenderId: '296762082745',
    projectId: 'connectify-e5692',
    storageBucket: 'connectify-e5692.appspot.com',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyDQRgs7FKLxsfSvVxtIgDzK26T4YZ0hCio',
    appId: '1:296762082745:ios:95d4690e95ea25b69da268',
    messagingSenderId: '296762082745',
    projectId: 'connectify-e5692',
    storageBucket: 'connectify-e5692.appspot.com',
    iosBundleId: 'com.connectify.connectifyParentApp',
  );

}