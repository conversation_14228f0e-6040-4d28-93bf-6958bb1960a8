import 'dart:io';

import 'package:connectify_app/firebase_options.dart';
import 'package:connectify_app/src/app.dart';
import 'package:connectify_app/src/shared/services/notifications/local_notifications_service.dart';
import 'package:fast_cached_network_image/fast_cached_network_image.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_restart_plus/flutter_restart_plus.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:path_provider/path_provider.dart';
import 'package:restart_app/restart_app.dart';
import 'package:xr_helper/xr_helper.dart';

Future<void> restartApp(BuildContext context) async {
  if (Platform.isIOS) {
    await FlutterRestartPlus().restartApp();
  } else {
    Restart.restartApp();
  }
}

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);

  String storageLocation = (await getApplicationDocumentsDirectory()).path;

  await Future.wait([
    FastCachedImageConfig.init(
      subDir: storageLocation,
    ),
    Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    ),
    GetStorageService.init(),
    // GetStorageService.clearLocalData(),
  ]);

  NotificationService.init();

  LocalNotificationsService.init();

  runApp(const ProviderScope(child: BaseApp()));
}
