name: connectify_app
description: "Connectify Application."

publish_to: 'none'

version: 1.0.33+33

environment:
  sdk: '>=3.5.0 <4.0.0'


dependencies:
  flutter:
    sdk: flutter

  flutter_localizations:
    sdk: flutter

  # * Best Package *
  xr_helper:
    path: packages/xr_helper

  cupertino_icons: ^1.0.6

  #? UI
  intl: any
  #? State Management
  flutter_riverpod: ^2.4.9
  riverpod: ^2.4.9
  hooks_riverpod: ^2.4.9
  flutter_hooks: ^0.20.4

  #? Equatable (Compare objects)
  equatable: ^2.0.5

  #? Dots Indicator
  dots_indicator: ^3.0.0

  #? Widgets
  fast_cached_network_image: ^1.3.2+4
  shimmer: ^3.0.0
  timeline_tile: ^2.0.0
  dotted_border: ^2.0.0+3
  flutter_screenutil: ^5.9.0
  file_picker: ^9.0.2
  permission_handler: ^11.0.0
  lottie: ^3.1.2
  google_fonts: ^6.1.0
  fl_chart: ^0.69.0
  auto_height_grid_view: ^1.0.0
  flutter_expandable_fab: ^2.0.0
  table_calendar:
  syncfusion_flutter_charts: ^27.1.50
  url_launcher: ^6.3.0
  flutter_rating_bar: ^4.0.1
  path_provider: ^2.1.5
  video_player: ^2.10.0
  video_thumbnail: ^0.5.6

  #? Images
  flutter_svg: ^2.0.9
  dio: ^5.8.0+1
  gal: ^2.3.1

  #? Firebase
  firebase_auth: ^5.5.1
  firebase_core: ^3.12.1

  #? OTP
  pin_code_fields: ^8.0.1
  circular_countdown_timer: ^0.2.3
  restart_app: ^1.3.2
  flutter_restart_plus: ^0.0.1
  awesome_notifications: ^0.10.1

  quickalert: ^1.1.0

  #? Generate Assets
  flutter_gen: ^5.4.0

  #? Helpers
  collection: ^1.15.0
  flutter_media_downloader: ^2.0.0
  in_app_update: ^4.2.3

dependency_overrides:
  archive: ^3.6.1
  win32: ^5.5.4
  intl: ^0.20.2

dev_dependencies:

  flutter_test:
    sdk: flutter

  build_runner:
  flutter_gen_runner:


  flutter_lints: ^4.0.0

#  flutter_launcher_icons: ^0.13.1
#  flutter_native_splash: ^2.3.9

#? dart run flutter_launcher_icons:main
#flutter_launcher_icons:
#  android: true
#  ios: true
#  remove_alpha_ios: true
#  image_path: "assets/images/logo.jpeg"
#
## ? dart run flutter_native_splash:create
#flutter_native_splash:
#  android: true
#  ios: true
#  web: false
#  fullscreen: false
#  color: '#ffffff'
#  image: 'assets/images/logo.jpeg'
#  android_12:
#    color: '#ffffff'
#    image: 'assets/images/logo.jpeg'



flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/animated/
    - assets/svg/

flutter_intl:
  enabled: true

