part of xr_helper;

class BaseMainDialog extends StatefulWidget {
  final Widget child;
  final double radius;

  const BaseMainDialog({
    super.key,
    required this.child,
    this.radius = AppRadius.baseRadius,
  });

  @override
  State<StatefulWidget> createState() => _MainDialog();
}

class _MainDialog extends State<BaseMainDialog>
    with SingleTickerProviderStateMixin {
  late AnimationController controller;
  late Animation<double> scaleAnimation;

  @override
  void initState() {
    controller = AnimationController(
        vsync: this, duration: const Duration(milliseconds: 500));
    scaleAnimation =
        CurvedAnimation(parent: controller, curve: Curves.elasticInOut);

    controller.addListener(() {
      setState(() {});
    });

    controller.forward();

    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Material(
        color: Colors.transparent,
        child: ScaleTransition(
          scale: scaleAnimation,
          child: Container(
              margin: const EdgeInsets.all(15),
              decoration: ShapeDecoration(
                  color: Colors.white,
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(widget.radius))),
              child: widget.child),
        ),
      ),
    );
  }
}
