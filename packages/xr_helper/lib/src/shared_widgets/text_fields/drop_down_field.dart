part of xr_helper;

class BaseDropDown extends StatelessWidget {
  final dynamic selectedValue;
  final String? label;
  final List<dynamic> data;
  final void Function(dynamic)? onChanged;
  final Widget? icon;
  final String Function(dynamic)? asString;
  final bool isRequired;
  final bool showTitle;
  final String? title;
  final bool isWhiteText;

  const BaseDropDown(
      {super.key,
      required this.onChanged,
      this.asString,
      this.title,
      required this.data,
      this.label,
      required this.selectedValue,
      this.isRequired = true,
      this.showTitle = true,
      this.isWhiteText = false,
      this.icon});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (title != null) ...[
          Text(
            title!,
            style: context.subTitle,
          ),
          context.smallGap,
        ],
        //! Text Field
        _dropDown(context),
      ],
    );
  }

  Widget _dropDown(BuildContext context) {
    return DropdownButtonFormField(
      value: selectedValue,
      isExpanded: true,
      borderRadius: BorderRadius.circular(AppRadius.baseRadius),
      items: data.map((e) {
        return DropdownMenuItem(
          value: e,
          child: FittedBox(
            fit: BoxFit.scaleDown,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 6),
              child: Text(
                asString != null ? asString!(e) : e.toString(),
                style: context.title.copyWith(
                  color: Colors.black,
                ),
              ),
            ),
          ),
        );
      }).toList(),
      onChanged: onChanged,
      decoration: InputDecoration(
        contentPadding: EdgeInsets.symmetric(
          vertical: AppSpaces.mediumPadding,
          horizontal:
              icon == null ? AppSpaces.mediumPadding : AppSpaces.smallPadding,
        ),
        label: label == null
            ? null
            : Padding(
                padding: const EdgeInsets.only(right: AppSpaces.smallPadding),
                child: FittedBox(
                    fit: BoxFit.scaleDown,
                    child: Text(label!,
                        style:
                            context.labelLarge.copyWith(color: Colors.black))),
              ),
        labelStyle: TextStyle(color: isWhiteText ? Colors.white : Colors.black),
        border: InputBorder.none,
        fillColor: isWhiteText
            ? ColorManager.white
            : ColorManager.fieldColor.withOpacity(0.3),
        prefixIcon: icon == null
            ? null
            : Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: icon,
              ),
      ),
      validator: (value) {
        if (value == null) {
          return 'Please select $label';
        }
        return null;
      },
    );
  }
}
