part of xr_helper;

class BaseDatePicker extends HookWidget {
  final ValueNotifier<DateTime?> selectedDateNotifier;
  final String label;
  final void Function(DateTime?)? onChanged;
  final bool isRequired;

  const BaseDatePicker({
    super.key,
    this.onChanged,
    required this.selectedDateNotifier,
    required this.label,
    this.isRequired = false,
  });

  @override
  Widget build(BuildContext context) {
    Widget errorText(String? errorText) {
      if (errorText == null) {
        return const SizedBox.shrink();
      }
      return Padding(
        padding: const EdgeInsets.only(left: 12, bottom: 8.0),
        child: Text(
          errorText,
          style: TextStyle(
            color: Theme.of(context).colorScheme.error,
          ),
        ),
      );
    }

    return ValueListenableBuilder(
      valueListenable: selectedDateNotifier,
      builder: (context, value, child) {
        final emptyDate = value == null;

        final selectedDate = value;

        return FormField(
          validator: (value) {
            if (isRequired && emptyDate) {
              return "Please select $label";
            }
            return null;
          },
          builder: (state) {
            return Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style:
                      context.labelLarge.copyWith(fontWeight: FontWeight.bold),
                ),
                context.smallGap,
                InkWell(
                  onTap: () async {
                    final date = await showDatePicker(
                      locale: Localizations.localeOf(context),
                      context: context,
                      initialDate: selectedDate == null ||
                              selectedDate.isBefore(DateTime(2000))
                          ? DateTime.now()
                          : selectedDate,
                      firstDate: DateTime(2000),
                      lastDate: DateTime(2100),
                      builder: (BuildContext context, Widget? child) {
                        return child!;
                      },
                    );
                    if (date == null) return;
                    selectedDateNotifier.value = date;

                    if (onChanged != null) onChanged!(date);
                    state.didChange(date);
                  },
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(AppRadius.baseRadius),
                      color: ColorManager.fieldColor.withOpacity(0.3),
                      border: Border.all(
                        color: state.hasError
                            ? Theme.of(context).colorScheme.error
                            : ColorManager.secondaryColor,
                      ),
                    ),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Row(
                          children: [
                            Expanded(
                              child: Text(
                                  emptyDate
                                      ? "Select $label"
                                      : DateFormat('yyyy-MM-dd')
                                          .format(selectedDate!),
                                  style: emptyDate
                                      ? context.hint
                                      : context.subTitle),
                            ),
                          ],
                        ),
                      ],
                    ),
                  ),
                ),
                errorText(state.errorText),
              ],
            ).decorated(
              radius: BorderRadius.circular(AppRadius.baseRadius),
              border: Border.all(
                color: state.hasError
                    ? Theme.of(context).colorScheme.error
                    : ColorManager.secondaryColor,
              ),
            );
          },
        );
      },
    );
  }
}
