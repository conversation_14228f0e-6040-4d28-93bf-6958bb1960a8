part of xr_helper;

class <PERSON><PERSON> extends StatelessWidget {
  final String label;
  final Widget? icon;
  final bool haveElevation;
  final void Function()? onPressed;
  final Color? color;
  final bool isPrefixIcon;
  final bool isOutLine;
  final bool isWhiteText;
  final bool isBold;
  final bool enabled;
  final bool isLoading;
  final Color? textColor;
  final Widget? loadingWidget;

  const Button({
    super.key,
    required this.label,
    this.haveElevation = true,
    required this.onPressed,
    this.icon,
    this.isPrefixIcon = false,
    this.isOutLine = false,
    this.isWhiteText = true,
    this.color = ColorManager.primaryColor,
    this.isBold = true,
    this.enabled = true,
    this.isLoading = false,
    this.loadingWidget,
    this.textColor,
  });

  @override
  Widget build(BuildContext context) {
    return AnimatedContainer(
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
      width: double.infinity,
      child: isLoading
          ? loadingWidget ?? const CircularProgressIndicator()
          : SizedBox(
              height: 60,
              child: ElevatedButton(
                  onPressed: onPressed,
                  style: ElevatedButton.styleFrom(
                    elevation: haveElevation && !isOutLine && enabled ? 3 : 1,
                    foregroundColor:
                        isOutLine ? color!.withOpacity(.1) : Colors.white,
                    surfaceTintColor:
                        isOutLine || !enabled ? Colors.transparent : color,
                    backgroundColor:
                        isOutLine || !enabled ? Colors.transparent : color,
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(AppRadius.baseRadius),
                    ),
                    side: isOutLine
                        ? BorderSide(
                            color: color!,
                            width: 1,
                          )
                        : BorderSide.none,
                  ),
                  child: _buildChild(context)),
            ),
    );
  }

  Widget _buildChild(BuildContext context) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        if (isPrefixIcon && icon != null)
          Row(
            children: [
              icon!,
              context.mediumGap,
            ],
          ),
        Expanded(
          child: Center(
            child: FittedBox(
              child: Text(
                label,
                maxLines: 2,
                textAlign: TextAlign.center,
                style: isWhiteText
                    ? context.whiteTitle.copyWith(
                        color: isOutLine ? color : null,
                        fontWeight: isBold ? FontWeight.bold : null,
                      )
                    : context.subTitle.copyWith(
                        fontWeight: isBold ? FontWeight.bold : null,
                        color: textColor,
                      ),
              ),
            ),
          ),
        ),
        if (icon != null && !isPrefixIcon)
          Row(
            children: [
              context.mediumGap,
              icon!,
            ],
          )
      ],
    );
  }
}
