part of xr_helper;

class BaseVM extends ChangeNotifier {
  //! Loading ====================================
  bool _isLoading = false;

  bool get isLoading => _isLoading;

  set setIsLoading(bool value) {
    _isLoading = value;
    notifyListeners();
  }

  //! Handle Success ====================================
  void handleSuccess(BuildContext context,
      [FlushBarType type = FlushBarType.add,
      bool isBack = true,
      bool isEng = true]) {
    if (type != FlushBarType.delete && isBack) {
      context.back();
    }

    context.showFlushBar(type: type, isEng: isEng);

    setIsLoading = false;
  }

  //! Base Function With Exception Handling ====================================
  Future<dynamic> baseFunction(
    BuildContext context,
    Function() mainFunction, {
    FlushBarType? type,
    bool isBack = true,
    bool isLoading = true,
    Function(BuildContext context)? additionalFunction,
  }) async {
    try {
      final isDelete = type == FlushBarType.delete;

      if (!isDelete) {
        setIsLoading = isLoading;
      }

      final function = await mainFunction();

      if (context.mounted) {
        await _handleSuccess(
          context,
          type: type,
          isBack: isBack,
          additionalFunction: additionalFunction,
        );
      }

      if (additionalFunction != null) {
        await additionalFunction(context);
      }

      if (function != null) return function;
    } on FetchDataException catch (error, s) {
      Log.e('🔴baseFunctionError🔴 $error\n$s');
      setIsLoading = false;

      if (context.mounted) {
        context.showBarMessage(error.toString().convertError, isError: true);

        // return error.toString().convertError;
        // context.showBarMessage('Error occurred', isError: true);
      }
      rethrow;
    } on SocketException {
      setIsLoading = false;

      // if (context.mounted) {
      //   context.showBarMessage('No Internet Connection', isError: true);
      // }
      rethrow;
    } on TimeoutException {
      setIsLoading = false;

      // if (context.mounted) {
      //   context.showBarMessage('Timeout', isError: true);
      // }
      rethrow;
    } catch (error, s) {
      Log.e('🔴baseFunctionError🔴 $error\n$s');
      setIsLoading = false;
      rethrow;
    }
  }

  Future<void> _handleSuccess(BuildContext context,
      {FlushBarType? type,
      bool isBack = true,
      Function(BuildContext context)? additionalFunction}) async {
    if (type != null) {
      if (!context.mounted) return;
      // handleSuccess(context, type, isBack);
    } else {
      setIsLoading = false;
    }
  }
}
