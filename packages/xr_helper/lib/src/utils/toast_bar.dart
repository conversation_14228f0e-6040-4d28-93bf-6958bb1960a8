// import 'package:flutter/material.dart';
// import 'package:xr_helper/xr_helper.dart';
//
// DelightToastBar toastBar(
//   BuildContext context,
//   String message, {
//   Color iconColor = Colors.white,
//   IconData icon = Icons.check_circle_outline,
//   bool isError = false,
//   String? title,
//   Color? color,
// }) {
//   return DelightToastBar(
//     autoDismiss: true,
//     builder: (context) {
//       return ToastCard(
//         leading: Icon(icon),
//         title: Text(message,
//             style:
//                 context.whiteLabelLarge.copyWith(fontWeight: FontWeight.bold)),
//         color: isError ? Colors.red : ColorManager.buttonColor,
//       );
//     },
//   )..show(context);
// }
