part of xr_helper;

class NotificationService {
  static Future<void> _firebaseMessagingBackgroundHandler(
      RemoteMessage message) async {
    if (Firebase.apps.isEmpty) await Firebase.initializeApp();
  }

  static void init() async {
    final fcm = FirebaseMessaging.instance;

    await fcm.requestPermission(
      alert: true,
      badge: true,
      provisional: false,
      sound: true,
    );

    fcm.setForegroundNotificationPresentationOptions(
        badge: true, alert: true, sound: true);

    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);

    fcm.subscribeToTopic('parents');

    return;
  }

  //? Get Token
  static Future<String> getToken() async {
    final fcm = FirebaseMessaging.instance;

    final token = await fcm.getToken();

    return token ?? '';
  }

  //? Subscribe to topic
  static Future<void> subscribeToTopic(String topic) async {
    final fcm = FirebaseMessaging.instance;

    Log.w('SUBSCRIBED_TO $topic');

    await fcm.subscribeToTopic(topic);
  }

  //? Unsubscribe from topic
  static Future<void> unsubscribeFromTopic(String topic) async {
    final fcm = FirebaseMessaging.instance;

    Log.w('UNSUBSCRIBED FROM $topic');

    await fcm.unsubscribeFromTopic(topic);
  }

  static Future<void> sendNotification(
      {required String title,
      required String body,
      required String userTokenOrTopic,
      bool isTopic = false}) async {
    // if (kDebugMode) return;
    const firebaseProjectId = 'connectify-e5692';

    Log.w('SentNotificationTo $userTokenOrTopic');

    const String fcmUrl =
        'https://fcm.googleapis.com/v1/projects/$firebaseProjectId/messages:send';

    final accessToken = await AccessTokenFirebase().getAccessToken();

    final Map<String, dynamic> message = {
      'message': {
        'token': isTopic ? '/topics/$userTokenOrTopic' : userTokenOrTopic,
        'notification': {
          'title': title,
          'body': body,
        },
      }
    };

    final response = await http.post(
      Uri.parse(fcmUrl),
      headers: {
        'Content-Type': 'application/json; charset=UTF-8',
        'Authorization': 'Bearer $accessToken',
      },
      body: jsonEncode(message),
    );

    if (response.statusCode == 200) {
      Log.w('NotificationSentSuccessfully ${response.body}');
    } else {
      Log.e('Failed to send notification: ${response.statusCode}');
      Log.e(response.body);
    }
  }
}

class AccessTokenFirebase {
  static const firebaseMessagingScope =
      'https://www.googleapis.com/auth/firebase.messaging';

  Future<String> getAccessToken() async {
    final jsonMap = ******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************;

    final client = await clientViaServiceAccount(
        ServiceAccountCredentials.fromJson(jsonMap), [firebaseMessagingScope]);

    final accessToken = client.credentials.accessToken.data;

    return accessToken;
  }
}

// part of xr_helper;
//
// class NotificationService {
//   static Future<void> _firebaseMessagingBackgroundHandler(
//       RemoteMessage message) async {
//     if (Firebase.apps.isEmpty) await Firebase.initializeApp();
//   }
//
//   static void init() async {
//     final fcm = FirebaseMessaging.instance;
//
//     await fcm.requestPermission(
//       alert: true,
//       badge: true,
//       provisional: false,
//       sound: true,
//     );
//
//     fcm.setForegroundNotificationPresentationOptions(
//         badge: true, alert: true, sound: true);
//
//     FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
//
//     // fcm.subscribeToTopic('vendors');
//
//     return;
//   }
//
//   //? Get Token
//   static Future<String> getToken() async {
//     final fcm = FirebaseMessaging.instance;
//
//     final token = await fcm.getToken();
//
//     return token ?? '';
//   }
//
//   //? Subscribe to topic
//   static Future<void> subscribeToTopic(String topic) async {
//     final fcm = FirebaseMessaging.instance;
//
//     Log.w('SUBSCRIBED TO $topic');
//
//     await fcm.subscribeToTopic(topic);
//   }
//
//   //? Unsubscribe from topic
//   static Future<void> unsubscribeFromTopic(String topic) async {
//     final fcm = FirebaseMessaging.instance;
//
//     Log.w('UNSUBSCRIBED FROM $topic');
//
//     await fcm.unsubscribeFromTopic(topic);
//   }
//
//   //! Send Notification
//   static Future sendNotification({
//     required String title,
//     required String body,
//     required String userTokenOrTopic,
//     bool isTopic = false,
//   }) async {
//     final data = jsonEncode(
//       <String, dynamic>{
//         'message': <String, dynamic>{
//           'topic': isTopic ? userTokenOrTopic : null,
//           'token': isTopic ? null : userTokenOrTopic,
//           'notification': <String, dynamic>{
//             'title': title,
//             'body': body,
//           },
//           'android': {
//             'priority': 'HIGH',
//             'notification': {
//               'click_action': 'FLUTTER_NOTIFICATION_CLICK',
//             },
//           },
//           'data': <String, dynamic>{'id': '1', 'status': 'done'},
//         },
//       },
//     );
//
//     final response = await http.post(
//       Uri.parse(
//           'https://fcm.googleapis.com/v1/projects/connectify-e5692/messages:send'),
//       headers: <String, String>{
//         'Content-Type': 'application/json',
//         // 'Authorization': 'Bearer $serverKey',
//       },
//       body: data,
//     );
//
//     Log.w('notificationResponse: ${response.body}');
//   }
// // static Future sendNotification(
// //   String serverKey, {
// //   required String title,
// //   required String body,
// //   required String userTokenOrTopic,
// //   bool isTopic = false,
// // }) async {
// //   final data = jsonEncode(
// //     <String, dynamic>{
// //       'notification': <String, dynamic>{
// //         'title': title,
// //         'body': body,
// //       },
// //       'priority': 'high',
// //       'data': <String, dynamic>{
// //         'click_action': 'FLUTTER_NOTIFICATION_CLICK',
// //         'id': '1',
// //         'status': 'done'
// //       },
// //       'to': isTopic ? '/topics/$userTokenOrTopic' : userTokenOrTopic,
// //     },
// //   );
// //
// //   final response = await http.post(
// //     Uri.parse('https://fcm.googleapis.com/fcm/send'),
// //     headers: <String, String>{
// //       'Content-Type': 'application/json',
// //       'Authorization': 'key=$serverKey',
// //     },
// //     body: data,
// //   );
// //
// //   Log.w('notificationResponse: ${response.body}');
// // }
// }
