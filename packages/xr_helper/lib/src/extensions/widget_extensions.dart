part of xr_helper;

extension WidgetExtenstions on Widget {
  //? Padding -----------------------------------------
  Widget paddingAll(double value) {
    return Padding(
      padding: EdgeInsets.all(value),
      child: this,
    );
  }

  Widget paddingSymmetric({double vertical = 0, double horizontal = 0}) {
    return Padding(
      padding: EdgeInsets.symmetric(vertical: vertical, horizontal: horizontal),
      child: this,
    );
  }

  Widget paddingOnly(
      {double top = 0, double bottom = 0, double left = 0, double right = 0}) {
    return Padding(
      padding:
          EdgeInsets.only(top: top, bottom: bottom, left: left, right: right),
      child: this,
    );
  }

  //? Align -----------------------------------------
  Widget align([AlignmentGeometry alignment = Alignment.center]) {
    return Align(
      alignment: alignment,
      child: this,
    );
  }

  //? Center -----------------------------------------
  Widget center() {
    return Center(
      child: this,
    );
  }

  //? SizedBox -----------------------------------------
  Widget sized({double? width, double? height}) {
    return SizedBox(
      width: width,
      height: height,
      child: this,
    );
  }

  //? Expanded -----------------------------------------
  Widget expanded({int flex = 1}) {
    return Expanded(
      flex: flex,
      child: this,
    );
  }

  //? Flexible -----------------------------------------
  Widget flexible({int flex = 1, FlexFit fit = FlexFit.loose}) {
    return Flexible(
      flex: flex,
      fit: fit,
      child: this,
    );
  }

  //? Container
  Widget decorated({
    Color? color,
    BorderRadius? radius,
    AlignmentGeometry? alignment,
    List<BoxShadow> shadow = const [],
    double? height,
    double? width,
    BoxBorder? border,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
  }) {
    return Container(
      height: height,
      width: width,
      alignment: alignment,
      padding: padding,
      margin: margin,
      decoration: BoxDecoration(
          color: color,
          borderRadius: radius,
          boxShadow: shadow,
          border: border),
      child: this,
    );
  }

  //? ScrollView
  Widget scroll({
    Axis scrollDirection = Axis.vertical,
    bool reverse = false,
    bool primary = true,
    ScrollPhysics? physics,
    ScrollController? controller,
    Clip clipBehavior = Clip.hardEdge,
    EdgeInsetsGeometry? padding,
  }) {
    return SingleChildScrollView(
      child: this,
      scrollDirection: scrollDirection,
      padding: padding,
      reverse: reverse,
      primary: primary,
      physics: physics,
      controller: controller,
      clipBehavior: clipBehavior,
    );
  }

  //? OnTap
  Widget onTap(Function() onTap) {
    return GestureDetector(
      onTap: onTap,
      child: this,
    );
  }

  //? OnTap with ripple effect
  Widget onTapWithRipple(
    Function() onTap, {
    double radius = AppRadius.baseRadius,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.all(Radius.circular(radius)),
      child: this,
    );
  }
}

extension TextWidgets on String {
  Widget baseText(
    BuildContext context,
    TextStyle style, {
    TextAlign? textAlign,
    TextOverflow? overflow,
    int? maxLines,
    bool? softWrap,
    TextDecoration? decoration,
    Color? color,
    double? letterSpacing,
    double? wordSpacing,
    double? height,
    Locale? locale,
    Paint? foreground,
    String? fontFamily,
    List<Shadow>? shadows,
    TextBaseline? textBaseline,
    double? fontSize,
    FontWeight? fontWeight,
    FontStyle? fontStyle,
  }) {
    return Text(
      this,
      style: style.copyWith(
        color: color,
        decoration: decoration,
        fontSize: fontSize,
        fontWeight: fontWeight,
        fontStyle: fontStyle,
        letterSpacing: letterSpacing,
        wordSpacing: wordSpacing,
        height: height,
        locale: locale,
        foreground: foreground,
        fontFamily: fontFamily,
        shadows: shadows,
        textBaseline: textBaseline,
      ),
      textAlign: textAlign,
      overflow: overflow,
      maxLines: maxLines,
      softWrap: softWrap,
    );
  }

  //? Label Large
  Widget labelLargeText(BuildContext context) {
    return Text(
      this,
      style: context.labelLarge,
    );
  }

  //? Label Medium
  Widget labelMediumText(BuildContext context) {
    return Text(
      this,
      style: context.labelMedium,
    );
  }
}

extension OnWillPopScope on Widget {
  Widget onWillPopMainScreen(Function() onWillPop) {
    return WillPopScope(
      onWillPop: () async {
        onWillPop();
        return true;
      },
      child: this,
    );
  }
}
