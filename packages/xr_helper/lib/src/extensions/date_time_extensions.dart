part of xr_helper;

extension DateTimeExtentions on DateTime? {
  String get formatDateToString {
    if (this == null) return '';
    return DateFormat('yyyy-MM-dd', 'en').format(this!);
  }

  String get formatToMonthName {
    if (this == null) return '';
    return DateFormat('MMMM').format(this!);
  }

  String get formatDateToTimeAndString {
    if (this == null) return '';
    return DateFormat('yyyy-MM-dd HH:mm', 'en').format(this!);
  }

  String get formatToDayName {
    if (this == null) return '';
    return DateFormat('EEEE', 'en').format(this!);
  }

  String get formatTime {
    if (this == null) return '';
    return DateFormat('HH:mm', 'en').format(this!);
  }

  //? isToday
  bool get isToday {
    if (this == null) return false;
    final now = DateTime.now();
    return now.day == this!.day &&
        now.month == this!.month &&
        now.year == this!.year;
  }
}
