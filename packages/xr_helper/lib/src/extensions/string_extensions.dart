part of xr_helper;

extension StringExtenstions on String? {
  //! Date Time ================================
  String get formatDateTimeToString {
    if (this == null || this!.isEmpty) return '';
    return DateFormat('yyyy/MM/dd').format(DateTime.parse(this!));
  }

  //? Get only month
  String get formatDateTimeToMonth {
    if (this == null || this!.isEmpty) return '';
    return DateFormat('MMMM').format(DateTime.parse(this!));
  }

  DateTime get formatStringToDateTime {
    if (this == null || this!.isEmpty) return DateTime.now();

    try {
      return DateTime.parse(this!);
    } catch (e) {
      try {
        return DateFormat('yyyy-MM-dd', 'ar').parse(this!);
      } catch (e) {
        return DateTime.now();
      }
    }
  }

  String? get filterMultiDropDownList {
    if (this == null || this!.isEmpty) return null;
    return this?.replaceAll('[', '').replaceAll(']', '');
  }

  //? convert error
//? convert this {"data":null,"error":{"status":400,"name":"ValidationError","message":"Invalid identifier or password","details":{}}}
//? to Invalid identifier or password
  String get convertError {
    if (this == null || this!.isEmpty) return '';
    // handle this error too
    // {"data":null,"error":{"status":400,"name":"ApplicationError","message":"Email already taken","details":{}}}
    Log.e('ERROR $this');

    if (!this!.contains('error')) return this!;

    final error = json.decode(this!);

    if (error['error'] == null) return '';

    return error['error']['message'];
  }

//? convert error
}

extension URLLauncher on String {
  Future<void> launchURL() async {
    if (await canLaunchUrl(Uri.parse(this))) {
      await launchUrl(Uri.parse(this));
    } else {
      throw 'Could not launch $this';
    }
  }

  //? Call Phone Number
  Future<void> call() async {
    if (await canLaunchUrl(Uri.parse('tel:$this'))) {
      await launchUrl(Uri.parse('tel:$this'));
    } else {
      throw 'Could not launch $this';
    }
  }
}
