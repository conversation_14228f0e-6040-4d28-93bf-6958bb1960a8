part of xr_helper;

class ColorManager {
  static const backgroundColor = Color(0xFFF7F7F7);
  static const primaryColor = Color(0xff724CDE);
  static const blueColor = Color(0xFF4378F4);
  static const darkPrimaryColor = Color(0xFF1154B6);
  static const secondaryColor = ColorManager.white;
  static const gradientBackground = [
    gradientColor,
    grey,
  ];

  static const gradientColor = Color(0xffccd3db);

  static const buttonColor = Color(0xff2FAB79);
  static const senderColor = Color(0xffF2EDFF);
  static const messageColor = Color(0xffFAFFED);
  static const greyIndicator = Color(0xff9B9B9B);
  static const tabBarColor = Color(0xffD9D9D9);
  static const blackBlue = Color(0xff364356);
  static const containerColor = Color(0xFFDDE1FF);
  static const fieldColor = Color(0xFFCBD5E1);
  static const bottomNavIconColor = Color(0xff212222);
  static const greyColor = Color(0xffF1F1F1);
  static const white = Color(0xFFFFFFFF);
  static const purple = Color(0xFF766CE2);
  static const lightBlue = Color(0xFF35BFEE);
  static const darkBlue = Color(0xFF163ECD);
  static const black = Color(0xFF000000);
  static const grey = Color(0xFFCBD5E1);
  static const highlightColor = Color(0xFFFFFFFF);
  static const shimmerBaseColor = Color(0xFFCECECE);
  static const cardColor = Color(0xFFEDEDED);
  static const darkGrey = Color(0xFF363636);
  static const lightGrey = Color(0xFFF2F1F1);
  static const iconColor = Color(0xFF727272);
  static const errorColor = Color(0xFFE74C3C);
  static const successColor = Color(0xFF2ECC71);
}
